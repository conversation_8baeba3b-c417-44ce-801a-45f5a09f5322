# __init__.py
from utils.logging_config import LoggerConfigurator
from .ToolList_window import Ui_<PERSON><PERSON><PERSON>ist_Window
from .Toolinfo_window import Ui_ToolInfo_Window
from .Setting_window import Ui_Setting_Window
from .Filter_window import Ui_Filter_Window
from .Event_window import Ui_Event_window
from .ModeSwitch_window import Ui_ModeSwitch_Window
from .RecordScript_window import Ui_RecordScript_window, RecordScriptWindow_implementation
from .CO2e_window import Ui_CO2e_Window
from .View_window import Ui_View_Window
from .Confirm_window import Ui_Confirm_Window
from .Remind_window import Ui_Remind_Window
from .messagebox_window import Ui_message_box_Window
from .Help_Window import Ui_Help_Window
from .pop_up_window import Ui_pop_up_Window
from .tool_tip_window import Ui_Tooltip_label, Ui_Tooltip_label_up
from .add_tool_window import Ui_add_tool_window
from .RecordSetting_window import Ui_RecordSetting_window
from .ScriptStatus_window import U<PERSON>_ScriptStatus_window
from .DraggableDialog import Draggable<PERSON>ialog

# from app.models.tcp_data import TcpData                       
# from utils.socket_thread import Socket<PERSON>lient

# 初始化並配置日誌系統
log_configurator = LoggerConfigurator()
logger = log_configurator.get_logger(__name__)