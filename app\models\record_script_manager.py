from PySide2.QtCore import QObject, Signal, QThread
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple
from config import RECORD_SCRIPT_DEFAULT_CONFIG
from app.models.script_worker import <PERSON><PERSON><PERSON><PERSON>or<PERSON>, StepState
from . import logger

class ScriptState(Enum):
    """Script execution states"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    CANCELLED = "cancelled"
    ERROR = "error"

class RecordScriptManager(QObject):
    """script management for Record Script feature"""
    # signals for GUI notification
    sig_status_updated = Signal(str)  # status_message
    sig_script_error = Signal(str, bool)  # error_message, user_action_required
    sig_script_finished = Signal()

    # outgoing signals for requests to controller
    sig_connect_tool = Signal(int)
    sig_disconnect_tool = Signal(int)
    sig_start_record = Signal()
    sig_stop_record = Signal()
    sig_max_record_time = Signal(int) #

    def __init__(self):
        super().__init__()
        self.record_script_config = dict(RECORD_SCRIPT_DEFAULT_CONFIG)
        self.user_action_required = self.record_script_config.get('user_action_on_error', True)
        
        self.script_name = ""
        self.script_count = 0 # just for script naming
        
        self.script_state = ScriptState.IDLE
        self.script_mode = None
        self.record_enabled = False
        self.script = None
        
        self.script_worker_thread = None
        self.script_worker = None

        logger.info("RecordScriptManager initialized")
    
    def validate_script(self, mode: str, input_script_data: List[Dict[int, Any]]) -> bool:
        """Validate script data"""

        # validate mode
        if mode not in ["standard", "machine", "timed"]:
            error_string = f"Invalid mode: {mode}"
            logger.warning(error_string)
            return False, error_string
        
        # validate script length
        if len(input_script_data) == 0:
            error_string = "Script data is empty"
            logger.warning(error_string)
            return False, error_string
        
        # validate standard mode script length = 1
        elif mode == "standard" and len(input_script_data) > 1:
            error_string = "Standard mode only allows up to one tool"
            logger.warning(error_string)
            return False, error_string
          
        # check each step in script data
        for step, step_data in enumerate(input_script_data):
            # get data
            tool_id = step_data.get('tool_id', None)
            duration = step_data.get('duration', None)

            # validate existence of tool_id and duration
            if tool_id is None or duration is None:
                error_string = f"Step {step} is missing tool_id or duration"
                logger.warning(error_string)
                return False, error_string
            
            # validate duration
            if duration <= 0:
                error_string = f"Duration {duration} of step {step} is not greater than 0"
                logger.warning(error_string)
                return False, error_string

        return True, ""
    
    def save_script(self, mode: str, record_enabled: bool, input_script_data: List[Dict[int, Any]]) -> bool:
        """validate and configure script"""
        # UI click save -> controller -> call this function
        logger.info(f"Saving script: {mode}, {input_script_data}")

         # state check
        if self.script_state != ScriptState.IDLE:
            error_string = f"Cannot save script in state: {self.script_state.value}"
            logger.warning(error_string)
            return False, error_string

        # validate script
        validated, error_string = self.validate_script(mode, input_script_data)
        if not validated:
            return False, error_string
        
        # configure script
        self.script_count += 1
        self.script_mode = mode
        self.record_enabled = record_enabled
        self.script = {
            "script_data": input_script_data,
            "mode": mode,
            "record_enabled": record_enabled,
        }
        self.script_name = f"{mode}_{self.script_count}"

        logger.info(f"Script {self.script_name} configured: {self.script}")
        return True, ""
    
    def start_script(self) -> bool:
        """Start script execution"""
        # state check
        if self.script_state != ScriptState.IDLE:
            logger.warning(f"Cannot start script in state: {self.script_state.value}")
            return False
        
        # check for existence of script
        if not self.script:
            logger.warning("Cannot start script: no script configured")
            return False
        
        # check mode
        if self.script_mode == "standard":
            # emit signal to controller to set max record time in record manager
            logger.warning(f"There is no script run for standard mode, only limited record time = {self.script['script_data'][0]['duration']}")
            self.sig_max_record_time.emit(self.script['script_data'][0]['duration'])
            return True
        
        logger.info(f"Starting script: {self.script_name}")
        
        try:
            # create thread
            logger.info("Creating ScriptWorker thread")
            self.script_worker_thread = QThread()

            # create worker
            logger.info("Creating ScriptWorker")
            self.script_worker = ScriptWorker(self.script, self.record_script_config)
            
            # connect script worker signals
            logger.info("Connecting ScriptWorker signals")
            self.script_worker.sig_connect_tool.connect(self.sig_connect_tool)
            self.script_worker.sig_disconnect_tool.connect(self.sig_disconnect_tool)
            self.script_worker.sig_start_record.connect(self.sig_start_record)
            self.script_worker.sig_stop_record.connect(self.sig_stop_record)
            self.script_worker.sig_status_updated.connect(self._on_status_updated)
            self.script_worker.sig_error_occurred.connect(self._on_error_occurred)
            self.script_worker.sig_finished.connect(self._on_worker_finished)
            self.script_worker.sig_finished.connect(self.script_worker.deleteLater)
            # self.script_worker.sig_finished.connect(self.script_worker_thread.quit)

            # connect thread signals
            self.script_worker_thread.started.connect(self.script_worker.run)
            self.script_worker_thread.finished.connect(self.script_worker_thread.deleteLater)
            
            # Start execution
            self.script_state = ScriptState.RUNNING
            logger.info("Starting ScriptWorker thread")
            self.script_worker.moveToThread(self.script_worker_thread)
            self.script_worker_thread.start()
            
            # Emit start signal
            self.sig_status_updated.emit(f"Script {self.script_name} started")
            logger.info("Script started successfully")
            return True
        
        except Exception as e:
            error_msg = f"Failed to start script: {str(e)}"
            logger.error(error_msg)
            self.script_state = ScriptState.IDLE
            # self.sig_script_error.emit(error_msg, self.record_script_config.get('user_action_on_error', True))
            return False
    
    def pause_script(self) -> bool:
        """Pause current script"""
        # state check
        if self.script_state != ScriptState.RUNNING:
            logger.warning(f"Cannot pause script in state: {self.script_state.value}")
            return False
        
        # check for existence of script worker
        if not self.script_worker:
            logger.warning("Cannot pause script: no worker available")
            return False
        
        # send pause signal to script worker
        logger.info("Pausing script execution")
        self.script_worker.sig_pause.emit()
        self.script_state = ScriptState.PAUSED
        return True
        
    
    def resume_script(self) -> bool:
        """Resume paused script"""
        # state check
        if self.script_state != ScriptState.PAUSED:
            logger.warning(f"Cannot resume script in state: {self.script_state.value}")
            return False
        
        # check for existence of script worker
        if not self.script_worker:
            logger.warning("Cannot resume script: no worker available")
            return False

        # send resume signal to script worker
        logger.info("Resuming script execution")
        self.script_worker.sig_resume.emit()
        self.script_state = ScriptState.RUNNING
        return True
    
    def cancel_script(self) -> bool:
        """Cancel current script"""
        # state check
        if self.script_state not in [ScriptState.RUNNING, ScriptState.PAUSED, ScriptState.ERROR]:
            logger.warning(f"Cannot cancel script in state: {self.script_state.value}")
            return False
        
        # check for existence of script worker
        if not self.script_worker:
            logger.warning("Cannot cancel script: no worker available")
            return False
        
        # send cancel signal to script worker
        logger.info("Cancelling script execution")
        self.script_worker.sig_cancel.emit()

        # update state
        self.script_state = ScriptState.CANCELLED
        
        return True
    
    def tool_connected(self):
        """notify script worker that tool is connected"""
        self.script_worker.sig_tool_connected.emit()
    
    def tool_disconnected(self):
        """notify script worker that tool is disconnected"""
        self.script_worker.sig_tool_disconnected.emit()

    def connection_error(self):
        """notify script worker that connection error occurred"""
        self.script_state = ScriptState.ERROR
        self.script_worker.sig_connection_error.emit()
    
    def record_error(self):
        """notify script worker that record error occurred"""
        self.script_state = ScriptState.ERROR
        self.script_worker.sig_record_error.emit()
    
    def user_action_skip(self) -> bool:
        """Skip current step"""
        # state check
        if self.script_state != ScriptState.ERROR:
            logger.warning(f"Cannot skip step in state: {self.script_state.value}")
            return False
        
        # check for existence of script worker
        if not self.script_worker:
            logger.warning("Cannot skip step: no worker available")
            return False
        
        # send skip signal to script worker
        logger.info("Skipping step")
        self.script_worker.sig_user_action_skip.emit()

        # update state 
        self.script_state = ScriptState.RUNNING

        return True
    
    def user_action_cancel(self) -> bool:
        """Cancel current script"""
        # state check
        if self.script_state != ScriptState.ERROR:
            logger.warning(f"Cannot cancel script in state: {self.script_state.value}")
            return False
        
        # check for existence of script worker
        if not self.script_worker:
            logger.warning("Cannot cancel script: no worker available")
            return False
        
        # send cancel signal to script worker
        logger.info("Cancelling script execution")
        self.script_worker.sig_user_action_cancel.emit()

        # update state 
        self.script_state = ScriptState.CANCELLED

        return True
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current script status"""
        return {
            'state': self.script_state.value,
            'script': self.script,
            'script_name': self.script_name,
            'is_running': self.script_state == ScriptState.RUNNING,
            'is_paused': self.script_state == ScriptState.PAUSED,
            'is_idle': self.script_state == ScriptState.IDLE
        }
    
    def reset_script(self) -> bool:
        """Reset script to idle state"""
        # cancel script if running 
        if self.script_state in [ScriptState.RUNNING, ScriptState.PAUSED]:
            logger.info(f"Cancelling script in state {self.script_state.value} before reset")
            self.cancel_script()

        logger.info(f"Resetting script in state {self.script_state.value} to idle state")

        # reset stuff
        self.script_state = ScriptState.IDLE
        self.script = None
        self.script_name = ""
        self.script_worker = None
        self.script_worker_thread = None
        
        logger.info("Script reset completed")
        return True

    def is_disconnecting(self):
        """check if script is disconnecting"""
        return self.script_worker.current_step_state == StepState.DISCONNECTING
    
    # Signal handlers
    def _on_status_updated(self, status_message: str):
        logger.debug(f"Status update: {status_message}")
        self.sig_status_updated.emit(status_message)

    def _on_error_occurred(self, error_message: str):
        logger.error(f"Error occurred: {error_message}")
        self.sig_script_error.emit(error_message, self.user_action_required)
        self.script_state = ScriptState.ERROR
    
    def _on_worker_finished(self):
        logger.info(f"Script finished in state: {self.script_state.value}")
        self.sig_script_finished.emit()
        self.script_state = ScriptState.IDLE
        
        # kill thread
        self.script_worker_thread.quit()