from PySide2.QtCore import Signal, QTimer, QElapsedTimer, QObject, QEventLoop
from typing import Dict, Any
from enum import Enum
from . import logger

class StepState(Enum):
    """Step states"""
    INIT = "init"
    CONNECTING = "connecting"
    PROCESSING = "processing"
    DISCONNECTING = "disconnecting"
    END = "end"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"
    WAITING_FOR_USER_ACTION = "waiting_for_user_action"

# custom skip exception
class SkipException(Exception):
    """Skip exception"""
    pass

# custom cancel exception
class CancelException(Exception):
    """Cancel exception"""
    pass

class ScriptWorker(QObject):
    """script execution worker"""

    # outgoing signals
    sig_connect_tool = Signal(int) # tool_id
    sig_disconnect_tool = Signal(int) # tool_id
    sig_start_record = Signal()
    sig_stop_record = Signal()
    sig_status_updated = Signal(str) # for display
    sig_finished = Signal()  # worker finished
    sig_error_occurred = Signal(str)  # error_message

    # incoming signals
    sig_pause = Signal()
    sig_resume = Signal()
    sig_cancel = Signal()
    sig_tool_connected = Signal() 
    sig_tool_disconnected = Signal() 
    sig_connection_error = Signal()
    sig_record_error = Signal()
    sig_user_action_skip = Signal() 
    sig_user_action_cancel = Signal()

    # quit event loop in wait_for_signal and wait_for_timer
    sig_quit_event_loop = Signal() 

    def __init__(self, script, record_script_config: Dict[str, Any]):
        super().__init__()
        self.script_data = script.get("script_data", [])
        self.mode = script.get("mode", "timed")
        self.record_enabled = script.get("record_enabled", False)
        self.record_script_config = record_script_config
        
        self.is_connected = False
        self.is_recording = False
        self.is_paused = False
        self.is_cancelled = False
        self.is_skipped = False
        
        self.current_step = 0
        self.current_step_state = None
        self.current_step_tool_id = None
        self.current_step_duration = None

        self.connect_attempt_count = 0
        self.max_connect_attempts = self.record_script_config.get('connection_retry_attempts', 3)
        self.connect_retry_interval = self.record_script_config.get('connection_retry_interval', 0.5)

        self.timer = QTimer(self, singleShot=True)
        self.elapsed_timer = QElapsedTimer()
        self.elapsed_time = 0
        self.remaining_time = 0

        # connect incoming signals
        self.connect_signals()

        logger.info("ScriptWorker initialized")

    def connect_signals(self):
        """connect incoming signals"""
        self.sig_pause.connect(self.pause)
        self.sig_resume.connect(self.resume)
        self.sig_cancel.connect(self.cancel)
        self.sig_tool_connected.connect(self.on_tool_connected)
        self.sig_tool_disconnected.connect(self.on_tool_disconnected)
        self.sig_connection_error.connect(self.on_connection_error)
        self.sig_record_error.connect(self.on_record_error)
        self.sig_user_action_skip.connect(self.on_user_action_skip)
        self.sig_user_action_cancel.connect(self.on_user_action_cancel)
    
    def disconnect_signals(self):
        """disconnect incoming signals"""
        self.sig_pause.disconnect()
        self.sig_resume.disconnect()
        self.sig_cancel.disconnect()
        self.sig_tool_connected.disconnect()
        self.sig_tool_disconnected.disconnect()
        self.sig_connection_error.disconnect()
        self.sig_record_error.disconnect()
        self.sig_user_action_skip.disconnect()
        self.sig_user_action_cancel.disconnect()

    def run(self):
        """Main execution loop"""
        try:
            logger.info("Starting script execution")

            if self.mode == "standard":
                # standard mode : script run not supported
                logger.error("Standard mode script run is not supported")
                self.sig_error_occurred.emit("Standard mode script run is not supported")
            
            elif self.mode == "timed" or self.mode == "machine":
                # execute timed or machine mode
                self.execute()
            
            else:
                # invalid mode
                logger.error(f"Invalid mode: {self.mode}")
                self.sig_error_occurred.emit(f"Invalid mode: {self.mode}")
        
        except Exception as e:
            # handle execution error
            error_msg = f"Script execution failed: {str(e)}"
            logger.error(error_msg)
            self.sig_error_occurred.emit(error_msg)
        
        finally:
            logger.info("Script execution finished")
            
            # disconnect incoming signals
            self.disconnect_signals()

            # emit finished signal
            self.sig_finished.emit()
            logger.info("signal_finished emitted")
    
    def execute(self):
        """execute script step by step"""

        logger.info(f"execute: {self.script_data}")

        # loop over script 
        while self.current_step < len(self.script_data) and not self.is_cancelled:
            try:
                logger.warning(f"current_step = {self.current_step}")
                # init step 
                self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：初始化")
                self.init_step()

                # process step (wait for duration)
                self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：執行")
                self.process_step()
                
                # end step
                self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：結束")
                self.end_step()

            except SkipException as e:
                logger.warning(f"skipped step {self.current_step}")
                self.sig_status_updated.emit(f"錄製腳本：跳過第[{self.current_step}]項")
                continue

            except CancelException as e:
                logger.warning(f"cancelled step {self.current_step}")
                self.sig_status_updated.emit(f"錄製腳本：取消執行")
                return
            
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                self.sig_error_occurred.emit(f"Unexpected error: {e}")
                return
            
            finally:
                self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：完成")
                self.set_next_step()

    def set_next_step(self):
        """set next step"""
        if self.mode == "timed":
            self.current_step += 1
        # TODO: machine mode
        logger.info(f"next step: {self.current_step}")

    def init_step(self):
        """init step variables"""

        # set state = INIT
        self.current_step_state = StepState.INIT
        logger.info(f"current_step_state = {self.current_step_state.value}")

        # get step data
        self.current_step_tool_id = self.script_data[self.current_step].get('tool_id', None)
        self.current_step_duration = self.script_data[self.current_step].get('duration', None)
        logger.info(f"tool_id = {self.current_step_tool_id}, duration = {self.current_step_duration}")

        # reset flags
        self.is_connected = False
        self.is_recording = False
        self.is_paused = False
        self.is_cancelled = False
        self.is_skipped = False

        # reset counter
        self.connect_attempt_count = 0

        # reset timer variables
        self.elapsed_time = 0
        self.elapsed_timer.invalidate()
        self.remaining_time = self.current_step_duration * 1000

        return 
    
    def connection_loop(self):
        """while loop to connect to tool"""

        # request connection and wait 
        while not self.is_connected and self.connect_attempt_count < self.max_connect_attempts and not self.is_skipped and not self.is_cancelled:
            logger.info(f"connecting to tool {self.current_step_tool_id}")
            if not self.connect_to_tool():
                self.on_step_error()
                self.check_skip_or_cancel()
                return
                
            logger.info(f"waiting for connect signal")
            self.wait_for_signal()

        # check for skip or cancel
        self.check_skip_or_cancel()

        return

    def process_step(self):
        """while loop to process step"""
        
        # 1. connect to tool
        self.connection_loop()
        
        # set state = PROCESSING
        self.current_step_state = StepState.PROCESSING
        logger.info(f"current_step_state = {self.current_step_state.value}")

        # 2. start recording
        self.start_record()

        # 3.wait for duration
        while self.remaining_time > 10 and not self.is_skipped and not self.is_cancelled: # error margin of 10 ms
            logger.info(f"waiting for duration = {self.remaining_time} milliseconds")
            self.wait_for_timer()
            self.update_remaining_time()

        # check for skip or cancel
        self.check_skip_or_cancel()

        return
    
    def end_step(self):
        """end step"""

        # 4. stop recording
        self.stop_record()

        # stop timer (safe cleanup)
        self.stop_timer()

        # 5. disconnect from tool
        self.disconnect_from_tool()

        return


    def check_skip_or_cancel(self):
        """check for skip or cancel"""

        # case: cancelled
        if self.is_cancelled:
            raise CancelException()
        
        # case: skipped
        if self.is_skipped:
            raise SkipException()
        
        # case: proceed
        return
        
    
    def wait_for_signal(self):
        """wait for signal from main thread"""
        # create event loop
        loop = QEventLoop(self)
        self.sig_quit_event_loop.connect(loop.quit)

        logger.debug(f"wait_for_signal: loop exec")
        
        # start event loop
        loop.exec_()

        logger.debug("wait_for_signal: loop quit")
    
    def wait_for_timer(self):
        """wait for duration in processing step"""
        # state check
        if self.current_step_state != StepState.PROCESSING:
            logger.warning(f"Cannot wait for timer in state: {self.current_step_state.value}")
            self.on_step_error()

        # create event loop
        loop = QEventLoop()
        self.sig_quit_event_loop.connect(loop.quit)

        # start timers
        self.elapsed_timer.start()
        self.timer.setInterval(self.remaining_time)
        self.timer.timeout.connect(loop.quit)
        self.timer.start()

        logger.debug(f"wait_for_timer, duration = {self.remaining_time}")
        
        # start event loop
        loop.exec_()

        logger.debug("wait_for_timer: loop quit")

    def pause(self):
        """pause script execution"""
        # check if already paused
        if self.is_paused:
            logger.warning("Script is already paused")
            return False

        logger.info(f"Script paused, step[{self.current_step}], tool[{self.current_step_tool_id}], duration[{self.current_step_duration}s]")
        logger.info(f"Current step state: {self.current_step_state.value}")

        # state-aware handling
        if self.current_step_state == StepState.PROCESSING:
            # stop timer and recording
            self.stop_timer()
            self.stop_record()
        elif self.current_step_state == StepState.CONNECTING:
            logger.info(f"paused during connection attempt {self.connect_attempt_count}")
        elif self.current_step_state == StepState.DISCONNECTING:
            logger.info(f"paused during disconnection")
        else: 
            logger.warning(f"Unexpected state for pause: {self.current_step_state.value}")

        # set paused flag
        self.is_paused = True

        # send status update signal
        self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：暫停")

        # wait for resume signal
        while self.is_paused and not self.is_skipped and not self.is_cancelled:
            logger.info(f"waiting for resume signal")
            self.wait_for_signal()

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()

    def resume(self):
        """resume script execution"""
        # check if script is paused
        if not self.is_paused:
            logger.warning("Script is not paused")
            return False
        
        logger.info(f"resuming script from state: {self.current_step_state.value}")

        # unset pause flag
        self.is_paused = False
        
        # state-aware handling
        if self.current_step_state == StepState.PROCESSING:
            logger.info("resuming processing")
        elif self.current_step_state == StepState.CONNECTING:
            if self.is_connected:
                logger.info(f"connection established during pause, proceeding to processing")
            else:
                logger.info(f"resuming connection attempt {self.connect_attempt_count}")
                self.connect_attempt_count -= 1 # deduct count to redo attempt
        elif self.current_step_state == StepState.DISCONNECTING:
            if not self.is_connected:
                logger.info(f"disconnection completed during pause, proceeding to ending step")
            else:
                logger.info(f"resuming disconnection")
        else:
            logger.warning(f"Unexpected state for resume: {self.current_step_state.value}")

        # send status update signal
        self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：繼續執行")

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()

    def cancel(self):
        """cancel script execution"""
        # set state
        self.current_step_state = StepState.CANCELLED

        # set cancelled flag
        self.is_cancelled = True

        # cleanup
        self.end_step()

        # send status update signal
        self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：取消執行")

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()

    def connect_to_tool(self) -> bool:
        """requests tool connection"""
        # state check
        if self.current_step_state != StepState.INIT and self.current_step_state != StepState.CONNECTING:
            logger.warning(f"Cannot connect to tool in state: {self.current_step_state.value}")
            return False
        
        # check if already connected
        if self.is_connected:
            logger.warning("Already connected")
            return False
        
        logger.info(f"Tool {self.current_step_tool_id} requested connection")

        # emit signal
        self.sig_connect_tool.emit(self.current_step_tool_id)

        # update state and connect count
        self.current_step_state = StepState.CONNECTING
        self.connect_attempt_count += 1  

        logger.info(f"trying to connect to tool {self.current_step_tool_id}: attempt {self.connect_attempt_count}/{self.max_connect_attempts}")

        return True
    
    def disconnect_from_tool(self) -> bool:
        """requests tool disconnection"""
        # state check
        if self.current_step_state != StepState.CANCELLED and self.current_step_state != StepState.SKIPPED and self.current_step_state != StepState.PROCESSING:
            logger.warning(f"Cannot disconnect from tool in state: {self.current_step_state.value}")
            return False
        
        # check if already disconnected
        if not self.is_connected:
            logger.warning("Already disconnected")
            return False
        
        logger.info(f"Tool {self.current_step_tool_id} requested disconnection")

        # emit signal
        self.sig_disconnect_tool.emit(self.current_step_tool_id)
        
        # update state
        if self.current_step_state == StepState.PROCESSING:
            self.current_step_state = StepState.DISCONNECTING

        return True   
    
    def start_record(self):
        """start recording"""
        # state check
        if self.current_step_state != StepState.PROCESSING:
            logger.warning(f"Cannot start recording in state: {self.current_step_state.value}")
            return 
        
        # check if record is enabled
        if not self.record_enabled:
            logger.warning("Record is not enabled")
            return 
        
        # check if already recording
        if self.is_recording:
            logger.warning("Already recording")
            return 
        
        logger.info(f"Starting recording for step {self.current_step}")

        # emit signal
        self.sig_start_record.emit()

        # update flag 
        self.is_recording = True

        return

    def stop_record(self):
        """stop recording"""
        # state check
        if self.current_step_state != StepState.END and self.current_step_state != StepState.CANCELLED and self.current_step_state != StepState.PROCESSING and self.current_step_state != StepState.SKIPPED:
            logger.warning(f"Cannot stop recording in state: {self.current_step_state.value}")
            return False
        
        # check if record is enabled
        if not self.record_enabled:
            logger.warning("Record is not enabled")
            return False
        
        # check if already not recording
        if not self.is_recording:
            logger.warning("Not recording")
            return False
        
        logger.info(f"Stopping recording for step {self.current_step}")

        # emit signal
        self.sig_stop_record.emit()

        # update flag 
        self.is_recording = False

    def stop_timer(self):
        """stop timer and update elapsed time"""
        logger.info("stop timer")

        if self.timer.isActive():
            self.timer.stop()
            self.update_remaining_time()

    def update_remaining_time(self):
        """update remaining time"""
        if self.elapsed_timer.isValid():
            self.elapsed_time += self.elapsed_timer.elapsed()
            self.remaining_time -= self.elapsed_timer.elapsed()
            self.elapsed_timer.invalidate()
            logger.info(f"elapsed time = {self.elapsed_time}")
            logger.info(f"remaining time = {self.remaining_time}")

            # send status update signal
            # self.sig_status_updated.emit(f"remaining time = {self.remaining_time}")

    def on_mcode(self):
        """(for machine mode) M-code from CNC received"""
        # jump to step of tool_id specitied by mcode
        pass

    def on_tool_connected(self):
        """tool connected"""
        # state check
        if self.current_step_state != StepState.CONNECTING:
            logger.warning(f"Cannot connect to tool in state: {self.current_step_state.value}")
            self.sig_error_occurred.emit(f"Cannot connect to tool in state: {self.current_step_state.value}")
            self.on_step_error()
            return
    
        logger.info(f"Tool {self.current_step_tool_id} connected")

        # update flag
        self.is_connected = True
        
        # check if script is paused during connection
        if self.is_paused:
            logger.info("Connection completed while paused, waiting for resume")

        # send status update signal
        self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：已連接刀把")

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()
        
        return
        

    def on_tool_disconnected(self):
        """tool disconnected"""
        # state check
        if self.current_step_state != StepState.DISCONNECTING and self.current_step_state != StepState.CANCELLED: # also processing?
            logger.warning(f"Cannot disconnect from tool in state: {self.current_step_state.value}")
            self.sig_error_occurred.emit(f"Cannot disconnect from tool in state: {self.current_step_state.value}")
            self.on_step_error()
            return
        
        logger.info(f"Tool {self.current_step_tool_id} disconnected")

        # update flag
        self.is_connected = False

        # check if script is paused during disconnection
        if self.is_paused:
            logger.info("Disconnection completed while paused, waiting for resume")

        # send status update signal
        self.sig_status_updated.emit(f"錄製腳本第[{self.current_step}]項：已斷線")

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()

        return

    def on_connection_error(self):
        """handle connection error"""
        logger.error(f"Connection error on step {self.current_step}: tool {self.current_step_tool_id}, state: {self.current_step_state.value}")

        # connection error occured during connection attempt
        if self.current_step_state == StepState.CONNECTING:
            # check if script is paused during connection
            if self.is_paused:
                logger.info("Connection error while paused, will retry on resume")
            # check if max attempts reached
            if self.connect_attempt_count >= self.max_connect_attempts:
                logger.error(f"Failed to connect to tool after {self.connect_attempt_count} attempts")
                self.sig_error_occurred.emit(f"Failed to connect to tool after {self.connect_attempt_count} attempts")
                self.on_step_error()
            else:
                # retry
                logger.error(f"Retrying to connect to tool {self.current_step_tool_id} (after attempt {self.connect_attempt_count}/{self.max_connect_attempts})")
                self.sig_error_occurred.emit(f"Failed to connect to tool after {self.connect_attempt_count} attempts")
        # connection error occured during processing or disconnection
        else:
            logger.error(f"Tool {self.current_step_tool_id} connection dropped")
            self.sig_error_occurred.emit(f"Tool {self.current_step_tool_id} connection dropped")
            self.on_step_error()

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()

    def on_record_error(self):
        """handle record error"""
        logger.error(f"Record error on step {self.current_step}: tool {self.current_step_tool_id}, state = {self.current_step_state.value}")

        # proceed to step error handling
        self.on_step_error()

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()
    
    def on_step_error(self):
        """handle step error"""
        logger.error(f"Step error on step {self.current_step}: tool {self.current_step_tool_id}, state = {self.current_step_state.value}")
        # end step
        self.end_step()

        # determine what to do next
        user_action_on_error = self.record_script_config.get('user_action_on_error', True)
        if not user_action_on_error:
            # user action is not required; proceed according to config
            action_on_error = self.record_script_config.get('action_on_error', 'skip')
            if action_on_error == 'skip':
                self.is_skipped = True
            elif action_on_error == 'stop':
                self.cancel()
            else:
                logger.error(f"Invalid action on error: {action_on_error}")
                self.cancel()
        else:
            # user action is required
            logger.info("requesting user action")

            self.current_step_state = StepState.WAITING_FOR_USER_ACTION
            self.sig_error_occurred.emit(f"User action required on step {self.current_step}")
            logger.info(f"waiting for user action signal")
            self.wait_for_signal()

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()
    
    def on_user_action_skip(self):
        """handle user action = skip"""
        # state check
        if self.current_step_state != StepState.WAITING_FOR_USER_ACTION:
            logger.warning(f"Cannot handle user action skip in state: {self.current_step_state.value}")
            self.cancel()
            return
        
        logger.info("user action = skip")

        # update state
        self.current_step_state = StepState.SKIPPED

        # set skip flag
        self.is_skipped = True

        # cleanup
        self.end_step()

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()

    def on_user_action_cancel(self):
        """handle user action = cancel"""
        # state check
        if self.current_step_state != StepState.WAITING_FOR_USER_ACTION:
            logger.warning(f"Cannot handle user action cancel in state: {self.current_step_state.value}")
            self.cancel()
            return
        
        logger.info("user action = cancel")

        # update state
        self.current_step_state = StepState.CANCELLED

        # cancel
        self.cancel()

        # quit event loop (signal handling finished)
        self.sig_quit_event_loop.emit()
