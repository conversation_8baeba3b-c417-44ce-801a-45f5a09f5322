import re
import os
from PySide2.QtWidgets import QLineEdit

class InputValidator:
    @staticmethod
    def validate_float(line_edit: QLineEdit):
        """ 限制輸入為負數或小數 """
        text = line_edit.text()
        # 允許的格式: 整數 (-1, 1)、小數 (-1.23, 0.5)、不允許單獨出現 `-` 或 `.`
        if not re.fullmatch(r"-?(?:\d+|\d*\.\d+)", text):
            line_edit.setText(re.sub(r"[^0-9.-]", "", text))

    @staticmethod
    def validate_int(line_edit: QLineEdit, max_length: int = None):
        """ 限制輸入為純整數，並可限制最大長度 """
        text = re.sub(r"[^\d]", "", line_edit.text())  # 移除非數字
        if max_length and len(text) > max_length:
            text = text[:max_length]  # 限制長度
        line_edit.setText(text)

    @staticmethod
    def validate_alphanumeric(line_edit: QLineEdit):
        """ 限制輸入為英文字母、數字和底線 """
        text = re.sub(r"[^\w]", "", line_edit.text())
        line_edit.setText(text)

    @staticmethod
    def validate_ip(line_edit: QLineEdit):
        """ 限制輸入為 IP 位址格式 (如 ***********) """
        text = re.sub(r"[^\d.]", "", line_edit.text())  # 只保留數字和點
        parts = text.split(".")
        if len(parts) > 4:
            parts = parts[:4]  # 限制最多 4 段
        text = ".".join(part[:3] for part in parts)  # 限制每段最多 3 位數
        line_edit.setText(text)

    @staticmethod
    def validate_mac(line_edit: QLineEdit):
        """ 限制輸入為 MAC 位址格式 (如 AA:BB:CC:DD:EE:FF) """
        text = re.sub(r"[^0-9A-Fa-f:]", "", line_edit.text().upper())  # 只允許 0-9 A-F 和 `:`
        parts = text.split(":")
        if len(parts) > 6:
            parts = parts[:6]  # 限制最多 6 段
        text = ":".join(part[:2] for part in parts)  # 限制每段最多 2 位數
        line_edit.setText(text)

    @staticmethod
    def apply_validator(line_edit: QLineEdit, validator_type: str, **kwargs):
        """
        根據 `validator_type` 對 `QLineEdit` 套用驗證方法。
        :param line_edit: 目標 QLineEdit
        :param validator_type: "float", "int", "alphanumeric", "ip", "mac"
        :param kwargs: 額外參數，如 max_length (int)
        """
        if validator_type == "float":
            line_edit.textChanged.connect(lambda: InputValidator.validate_float(line_edit))
        elif validator_type == "int":
            max_length = kwargs.get("max_length", None)
            line_edit.textChanged.connect(lambda: InputValidator.validate_int(line_edit, max_length))
        elif validator_type == "alphanumeric":
            line_edit.textChanged.connect(lambda: InputValidator.validate_alphanumeric(line_edit))
        elif validator_type == "ip":
            line_edit.textChanged.connect(lambda: InputValidator.validate_ip(line_edit))
        elif validator_type == "mac":
            line_edit.textChanged.connect(lambda: InputValidator.validate_mac(line_edit))

    @staticmethod
    def validate_toolinfo_fields(ui_toolinfo_window):
        """ 驗證刀把資訊欄位是否填寫完整，返回 (是否有效, 錯誤訊息) """
        # 檢查基本資訊欄位
        toolname = ui_toolinfo_window.toolinfoName_lineEdit.text().strip()
        toolip = ui_toolinfo_window.toolinfoIP_lineEdit.text().strip()
        sample_rate = ui_toolinfo_window.toolinfoFrequency_lineEdit.text().strip()
        
        if not toolname:
            return False, "錯誤：工具名稱不能為空！"
        
        if not toolip:
            return False, "錯誤：工具IP地址不能為空！"
        
        if not sample_rate:
            return False, "錯誤：取樣頻率不能為空！"
        
        # 驗證取樣頻率是否為有效數字
        try:
            float(sample_rate)
        except ValueError:
            return False, "錯誤：取樣頻率必須是有效的數字！"
        
        # 檢查校正係數欄位
        tare_fields = {
            "tare_xv": (ui_toolinfo_window.Tareinfo_Fx_lineedit.text().strip(), "校正係數 Fx"),
            "tare_yv": (ui_toolinfo_window.Tareinfo_Fy_lineedit.text().strip(), "校正係數 Fy"),
            "tare_zv": (ui_toolinfo_window.Tareinfo_Fz_lineedit.text().strip(), "校正係數 Fz"),
            "tare_tv": (ui_toolinfo_window.Tareinfo_T_lineedit.text().strip(), "校正係數 T")
        }
        
        for field_key, (field_value, field_name) in tare_fields.items():
            if not field_value:
                return False, f"錯誤：{field_name}不能為空！"
            try:
                float(field_value)
            except ValueError:
                return False, f"錯誤：{field_name}必須是有效的數字！"
        
        # 檢查換算係數欄位
        linear_fields = {
            "Linear_x": (ui_toolinfo_window.conversion_Fx_lineEdit.text().strip(), "換算係數 Fx"),
            "Linear_y": (ui_toolinfo_window.conversion_Fy_lineEdit.text().strip(), "換算係數 Fy"),
            "Linear_z": (ui_toolinfo_window.conversion_Fz_lineEdit.text().strip(), "換算係數 Fz"),
            "Linear_t": (ui_toolinfo_window.conversion_T_lineEdit.text().strip(), "換算係數 T")
        }
        
        for field_key, (field_value, field_name) in linear_fields.items():
            if not field_value:
                return False, f"錯誤：{field_name}不能為空！"
            try:
                float(field_value)
            except ValueError:
                return False, f"錯誤：{field_name}必須是有效的數字！"
        
        # 檢查轉換單位欄位
        conversion_tol = ui_toolinfo_window.conversion_TOL_lineEdit.text().strip()
        if not conversion_tol:
            return False, "錯誤：轉換單位 TOL 不能為空！"
        
        try:
            float(conversion_tol)
        except ValueError:
            return False, "錯誤：轉換單位 TOL 必須是有效的數字！"
        
        return True, ""

    @staticmethod
    def validate_setting_fields(ui_setting_window):
        """ 驗證設定視窗的所有欄位，返回 (是否有效, 錯誤訊息) """
        # 檢查資料夾路徑
        folder_path = ui_setting_window.FolderPath_lineEdit.text().strip()
        
        # 檢查路徑是否為空
        if not folder_path:
            return False, "錯誤：檔案路徑不能為空！"
        
        # 檢查路徑是否存在
        if not os.path.exists(folder_path):
            return False, f"錯誤：指定的路徑不存在！\n路徑：{folder_path}"
        
        # 檢查是否為資料夾
        if not os.path.isdir(folder_path):
            return False, f"錯誤：指定的路徑不是資料夾！\n路徑：{folder_path}"
        
        # 檢查是否有寫入權限
        if not os.access(folder_path, os.W_OK):
            return False, f"錯誤：您沒有該資料夾的寫入權限！\n路徑：{folder_path}"
        
        # 檢查檔案名稱
        file_name = ui_setting_window.txtname_lineEdit.text().strip()
        
        # 檢查檔案名稱是否為空
        if not file_name:
            return False, "錯誤：檔案名稱不能為空！"
        
        # 檢查檔案名稱是否以 .msra 結尾
        if not file_name.lower().endswith('.msra'):
            return False, "錯誤：檔案名稱必須以 .msra 結尾！"
        
        # 檢查檔案名稱是否包含不合法字符
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
        for char in invalid_chars:
            if char in file_name:
                return False, f"錯誤：檔案名稱不能包含特殊字符：{char}"
        
        return True, ""

    @staticmethod
    def validate_view_warning_fields(ui_view_window):
        """ 驗證警示/警告設定欄位是否填寫完整，返回 (是否有效, 錯誤訊息) """
        
        # 檢查 CF 相關欄位
        cf_fields = {
            "CF_max_lineedit": (ui_view_window.CF_max_lineedit.text().strip(), "CF 最大值"),
            "CF_min_lineedit": (ui_view_window.CF_min_lineedit.text().strip(), "CF 最小值"),
            "CF_warning_lineedit": (ui_view_window.CF_warning_lineedit.text().strip(), "CF 警告值"),
            "CF_alarm_lineedit": (ui_view_window.CF_alarm_lineedit.text().strip(), "CF警示值")
        }

        for field_key, (field_value, field_name) in cf_fields.items():
            if field_value and float(field_value) < 0:
                return False, f"錯誤：{field_name}不能為負數！"
            elif field_value and field_value != "0.0":  # 允許空值或預設值
                try:
                    float(field_value)
                except ValueError:
                    return False, f"錯誤：{field_name}必須是有效的數字！"

        # 檢查 FxFy 相關欄位
        fxfy_fields = {
            "FxFy_pos_peak_lineedit": (ui_view_window.FxFy_pos_peak_lineedit.text().strip(), "FxFy 正峰值"),
            "FxFy_neg_peak_lineedit": (ui_view_window.FxFy_neg_peak_lineedit.text().strip(), "FxFy 負峰值")
        }
        
        for field_key, (field_value, field_name) in fxfy_fields.items():
            if field_value and field_value != "0.0":  # 允許空值或預設值
                try:
                    float(field_value)
                except ValueError:
                    return False, f"錯誤：{field_name}必須是有效的數字！"

        # 檢查 Fz 相關欄位
        fz_fields = {
            "Fz_pos_peak_lineedit": (ui_view_window.Fz_pos_peak_lineedit.text().strip(), "Fz 正峰值"),
            "Fz_neg_peak_lineedit": (ui_view_window.Fz_neg_peak_lineedit.text().strip(), "Fz 負峰值"),
            "Fz_pos_alarm_lineedit": (ui_view_window.Fz_pos_alarm_lineedit.text().strip(), "Fz 正警示值"),
            "Fz_neg_alarm_lineedit": (ui_view_window.Fz_neg_alarm_lineedit.text().strip(), "Fz 負警示值"),
            "Fz_pos_warning_lineedit": (ui_view_window.Fz_pos_warning_lineedit.text().strip(), "Fz 正警告值"),
            "Fz_neg_warning_lineedit": (ui_view_window.Fz_neg_warning_lineedit.text().strip(), "Fz 負警告值")
        }

        if ui_view_window.Fz_pos_peak_lineedit.text().strip() and float(ui_view_window.Fz_pos_peak_lineedit.text().strip()) < 0:
            return False, "錯誤：Fz 正峰值不能為負數！"
        if ui_view_window.Fz_neg_peak_lineedit.text().strip() and float(ui_view_window.Fz_neg_peak_lineedit.text().strip()) > 0:
            return False, "錯誤：Fz 負峰值不能為正數！"
        
        for field_key, (field_value, field_name) in fz_fields.items():
            if field_value and field_value != "0.0":  # 允許空值或預設值
                try:
                    float(field_value)
                except ValueError:
                    return False, f"錯誤：{field_name}必須是有效的數字！"

        # 檢查 Torque 相關欄位
        torque_fields = {
            "Torque_pos_peak_lineedit": (ui_view_window.Torque_pos_peak_lineedit.text().strip(), "扭矩 正峰值"),
            "Torque_neg_peak_lineedit": (ui_view_window.Torque_neg_peak_lineedit.text().strip(), "扭矩 負峰值"),
            "Torque_pos_alarm_lineedit": (ui_view_window.Torque_pos_alarm_lineedit.text().strip(), "扭矩 正警示值"),
            "Torque_neg_alarm_lineedit": (ui_view_window.Torque_neg_alarm_lineedit.text().strip(), "扭矩 負警示值"),
            "Torque_pos_warning_lineedit": (ui_view_window.Torque_pos_warning_lineedit.text().strip(), "扭矩 正警告值"),
            "Torque_neg_warning_lineedit": (ui_view_window.Torque_neg_warning_lineedit.text().strip(), "扭矩 負警告值")
        }
        
        for field_key, (field_value, field_name) in torque_fields.items():
            if field_value and field_value != "0.0":  # 允許空值或預設值
                try:
                    float(field_value)
                except ValueError:
                    return False, f"錯誤：{field_name}必須是有效的數字！"

        # 檢查 Temp 相關欄位
        temp_fields = {
            "Temp_max_lineedit": (ui_view_window.Temp_max_lineedit.text().strip(), "溫度 最大值"),
            "Temp_min_lineedit": (ui_view_window.Temp_min_lineedit.text().strip(), "溫度 最小值"),
            "Temp_warning_lineedit": (ui_view_window.Temp_warning_lineedit.text().strip(), "溫度 警告值"),
            "Temp_alarm_lineedit": (ui_view_window.Temp_alarm_lineedit.text().strip(), "溫度 警示值")
        }
        
        for field_key, (field_value, field_name) in temp_fields.items():
            if field_value and field_value != "0.0":  # 允許空值或預設值
                try:
                    float(field_value)
                except ValueError:
                    return False, f"錯誤：{field_name}必須是有效的數字！"
        
        return True, ""

    @staticmethod
    def validate_auto_recording_fields(ui_record_setting_window):
        """ 驗證自動錄製設定欄位是否填寫完整，返回 (是否有效, 錯誤訊息) """
        
        # 檢查錄製時間相關欄位 (整數)
        time_fields = {
            "record_seconds_lineEdit": (ui_record_setting_window.record_seconds_lineEdit.text().strip(), "錄製秒數"),
            "pre_record_seconds_lineEdit": (ui_record_setting_window.pre_record_seconds_lineEdit.text().strip(), "預錄製秒數")
        }
        
        for field_key, (field_value, field_name) in time_fields.items():
            if field_value:  # 檢查非空值
                try:
                    int_value = int(field_value)
                    if int_value < 0:
                        return False, f"錯誤：{field_name}不能為負數！"
                except ValueError:
                    return False, f"錯誤：{field_name}必須是有效的整數！"
        
        # 檢查閾值相關欄位 (浮點數)
        threshold_fields = {
            "cf_threshold_lineEdit": (ui_record_setting_window.cf_threshold_lineEdit.text().strip(), "CF 閾值"),
            "fz_threshold_lineEdit": (ui_record_setting_window.fz_threshold_lineEdit.text().strip(), "Fz 閾值"),
            "t_threshold_lineEdit": (ui_record_setting_window.t_threshold_lineEdit.text().strip(), "扭矩閾值")
        }
        
        for field_key, (field_value, field_name) in threshold_fields.items():
            if field_value and field_value != "0.0":  # 允許空值或預設值
                try:
                    float_value = float(field_value)
                    #if float_value < 0:
                    #   return False, f"錯誤：{field_name}不能為負數！"
                except ValueError:
                    return False, f"錯誤：{field_name}必須是有效的數字！"
        
        return True, ""
