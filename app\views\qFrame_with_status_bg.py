from PySide2.QtCore import Slot
from PySide2.QtWidgets import <PERSON><PERSON>rame, QWidget
from utils.enums import StressSafetyStatus


class QFrameWithStatusBG(QFrame):
    def __init__(self, parent:QWidget, name:str, status:StressSafetyStatus):
        super().__init__(parent)
        self.setObjectName(name)
        self.status = status

    @property
    def status(self) -> StressSafetyStatus:
        return self._status
    
    @status.setter
    def status(self, status:StressSafetyStatus):
        self._status = status
        self._set_background_by_status(self._status)
    
    @Slot(StressSafetyStatus)
    def set_status(self, status:StressSafetyStatus):
        self.status = status
    
    def _set_background_by_status(self, status:StressSafetyStatus):
        r, g, b, a = status.color.getRgb()
        self.setStyleSheet(f"""QFrame#bottom_frame {{
            background-color: qlineargradient(
                spread:pad, 
                x1:0, y1:1, 
                x2:0, y2:0, 
                stop:0 rgba({r}, {g}, {b}, {a}), 
                stop:1 rgba(0, 0, 0, 72)
            );
        }}""")
