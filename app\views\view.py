# from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget
# from PyQt5.QtGui import QStandardItemModel  # 用於創建表格模型
import sys
import time
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from PySide2.QtWebEngineWidgets import *
from app.main_window import Ui_main_window  # 匯入生成的 UI 類
from config import *
from app.dialogs import (
    Ui_ToolList_Window,
    Ui_ToolInfo_Window,
    Ui_Setting_Window,
    Ui_Filter_Window,
    Ui_Event_window,
    Ui_ModeSwitch_Window,
    Ui_RecordScript_window,
    RecordScriptWindow_implementation,
    Ui_Help_Window,
    Ui_CO2e_Window,
    Ui_View_Window,
    Ui_Confirm_Window,
    Ui_Remind_Window,
    Ui_message_box_Window,
    Ui_pop_up_Window,
    Ui_Tooltip_label,
    Ui_Tooltip_label_up,
    Ui_add_tool_window,
    Ui_RecordSetting_window,
    Ui_ScriptStatus_window,
    DraggableDialog,
)
from app.views.opengl_colorbar import DrawColorBar  # 引入自定義的 OpenGLWidget
from app.views.pyqtopengl_widget import Animation_3D  # 引入自定義的 OpenGLWidget
from app.views.opengl_bending import Animation_Bending
from app.views.opengl_time_series import Animation_Tension
from app.views.inputValidator import InputValidator  # 引入自定義的輸入驗證器
from utils.ui_scaling import apply_scaling_to_window
from utils.license_data import *
import system_image.system_image_rc
from PySide2.QtCore import QObject, Signal
import numpy as np
from functools import partial
from app.models.data_comparer import DataComparer
from app.models.network_device_manager import check_mac_in_database, check_mac_in_network
from utils.enums import StressSafetyStatus
from . import logger  # 從同一個包導入 logger
import os

version = "1.03.603.121"
set_mvc_version(version)

class View(QWidget, Ui_main_window):
    sigUpdateOpenglWidget = Signal(object, object)
    sigUpdateBending = Signal(np.ndarray, np.ndarray, np.ndarray)
    sigUpdateTension = Signal(np.ndarray)
    sigUpdateTorque = Signal(np.ndarray)
    sigUpdateTemp = Signal(np.ndarray)
    view_current_data = Signal(object)  # current_data AS eventLinkBtn - can be dict (tool config) or np.ndarray (sensor data)
    sigUpdateSafetyStatus = Signal(StressSafetyStatus)
    update_co2_data = Signal(dict)
    sigSaveFileData = Signal(dict)
    sigUpdateToolList = Signal()  # 新增信號用於更新工具列表
    sigDoSystemCheck = Signal(dict) # 發送信號給 controller 開始刀把系統檢查
    sigDoTare = Signal() # 讓 Toolinfo_window 發送信號給 controller 的 Tare_holder_window
    sigSaveToolInfo = Signal(dict) # 讓 Toolinfo_window 發送信號給 controller 存檔
    sigSaveAutoRecordingSettings = Signal(dict) # 發送信號給 controller 儲存自動錄製設定
    sigViewRWFileData = Signal(dict) # 發送信號給 controller 儲存/讀取 View 設定
    sigCloseEvent = Signal() # 發送信號給 controller 關閉軟體並確認是否要儲存
    sigSaveStartScript = Signal(str, bool, list) # tool_id, record_enabled, script_data
    sigScriptAction = Signal(str) # 發送信號給 controller 暫停/繼續/跳過/取消 腳本
    
    # Error handling signals 
    error_action_selected = Signal(str, str)  # Emits error_type, selected_action

    def __init__(self):
        logger.debug("Initializing View")
        super().__init__()
        self.setupUi(self)  # 初始化 UI 元素
 
        self.test_count = 0 # 測試用

        # 計時器相關
        self.timer_id = None  # 用來存儲計時器 ID
        self.count = 0  # 計數
        self.Battery_Check_Time = None
        self.Battery_percent = -1
        self.plot_type = 'scatter'  # 預設為 3D 圖
        self.msra_file_name = None
        self.connect_mac = None  # 連接的 MAC 地址

        # TODO : 顯示小數位數之後加入，show_Setting_window，DB machradar_setting 可以取得設定值
        self.display_decimal_places = DEFAULT_DISPLAY_DECIMALS 

        self.Toollist_window = None  # 確保變數存在
        self.Toolinfo_window = None  # 確保變數存在
        self.System_check_window = None
        self.Setting_window = None
        self.Filter_window = None
        self.View_window = None
        self.ModeSwitch_window = None
        self.RecordScript_window = None
        self.Help_window = None
        self.CO2e_window = None
        self.Event_window = None
        self.ScriptStatus_window = None  # ScriptStatus window

        #濾波器 參數
        self.filter_data = DEFAULT_FILTER.copy()
        
        # 顯示 預設參數 plus(+) or minus(-) 
        self.view_read_data = False
        self.view_setting_data = {
            "CF_Auto_value": {
                "state": False,
                "maximum": 10.0,
                "minimum": 0.0,
                "warning": 0.0,
                "alarm": 0.0
            },
            "FxFy_Auto_value": {
                "state": False,
                "peak_plus": 10.0,
                "peak_minus": -10.0
            },
            "Fz_Auto_value":  {
                "state": False,
                "peak_plus": 10.0,
                "peak_minus": -10.0,
                "alarm_plus": 0.0,
                "alarm_minus": 0.0,
                "warning_plus": 0.0,
                "warning_minus": 0.0
            },
            "Torque_Auto_value":{
                "state": False,
                "peak_plus": 10.0,
                "peak_minus": -10.0,
                "alarm_plus": 0.0,
                "alarm_minus": 0.0,
                "warning_plus": 0.0,
                "warning_minus": 0.0
            },
            "Temp_Auto_value": {
                "state": False,
                "maximum": 60.0,
                "minimum": 30.0,
                "warning": 50.0,
                "alarm": 55.0
            },
            "color_spectrum":0,
            "view_OpenAW_value": {
                "state": False
            },
            "view_HideRing_value": {
                "state": True
            },
            "view2_HideScale_value": {
                "state": False
            }
        }

        self.CO2_data = dict(DEFAULT_CO2_DATA)

        # 替換圖表 OR 3D
        self.replace_chart()
        # 信號綁定
        self.signal_binding()
        # tooltip事件綁定
        self.tooltip_binding()
        # tooltip事件綁定
        self.tooltip_binding_up()
        # tab事件綁定
        self.tab_binding()

    def replace_chart(self):
        """替換圖表"""
       # 替換 colorbar
        self.opengl_colorbar = DrawColorBar(self.colorbar)  # 設定 parent 為 self.plot3D
        self.horizontalLayout.replaceWidget(self.colorbar, self.opengl_colorbar)
        self.colorbar.deleteLater()  # 刪除原始的 QOpenGLWidget
       
        # 替換第一個 QOpenGLWidget
        self.opengl_widget_instance_1 = Animation_3D(self.plot3D_opengl)
        self.horizontalLayout.replaceWidget(
            self.plot3D_opengl, self.opengl_widget_instance_1
        )
        self.plot3D_opengl.deleteLater()  # 刪除原始的 QOpenGLWidget
        # TODO: 設置 Ring 顯示
        self.opengl_widget_instance_1.set_ring_visibility(True)

        # 創建新的 Animation_Bending
        self.opengl_widget_bending = Animation_Bending()

        # 確保佈局正確替換 QOpenGLWidget
        self.Fx_Fy_CFH_layout.replaceWidget(
            self.Fx_Fy_CF_opengl, self.opengl_widget_bending
        )

        # 移除舊的 QOpenGLWidget
        self.Fx_Fy_CF_opengl.setParent(None)
        self.Fx_Fy_CF_opengl.deleteLater()

        # 確保新的 widget 被正確加入佈局
        self.Fx_Fy_CFH_layout.addWidget(self.opengl_widget_bending)

        # 調整佈局的比例，確保新的 widget 佔據合適空間
        self.Fx_Fy_CFH_layout.setStretch(0, 15)  # 根據你的 UI 結構調整
        self.Fx_Fy_CFH_layout.setStretch(1, 120)  # 確保不會壓縮新的 widget

        # 創建新的 Tension_Bending
        self.opengl_widget_tension = Animation_Tension()

        # 確保佈局正確替換 QOpenGLWidget
        self.TorqueV_layout_2.replaceWidget(
            self.Torque_opengl_2, self.opengl_widget_tension
        )

        # 移除舊的 QOpenGLWidget
        self.Torque_opengl_2.setParent(None)
        self.Torque_opengl_2.deleteLater()

        # 確保新的 widget 被正確加入佈局
        self.TorqueV_layout_2.addWidget(self.opengl_widget_tension)

        # 調整佈局的比例，確保新的 widget 佔據合適空間
        self.TorqueV_layout_2.setStretch(1, 20)  # 根據你的 UI 結構調整
        self.TorqueV_layout_2.setStretch(2, 120)  # 確保不會壓縮新的 widget

        # 創建新的 Tension_Bending
        self.opengl_widget_torque = Animation_Tension()

        # 確保佈局正確替換 QOpenGLWidget
        self.TorqueV_layout.replaceWidget(self.Torque_opengl, self.opengl_widget_torque)

        # 移除舊的 QOpenGLWidget
        self.Torque_opengl.setParent(None)
        self.Torque_opengl.deleteLater()

        # 確保新的 widget 被正確加入佈局
        self.TorqueV_layout.addWidget(self.opengl_widget_torque)

        # 調整佈局的比例，確保新的 widget 佔據合適空間
        self.TorqueV_layout.setStretch(1, 20)  # 根據你的 UI 結構調整

        # 創建新的 Tension_Bending
        self.opengl_widget_temp = Animation_Tension()

        # 確保佈局正確替換 QOpenGLWidget
        self.TorqueV_layout_3.replaceWidget(
            self.Torque_opengl_3, self.opengl_widget_temp
        )

        # 移除舊的 QOpenGLWidget
        self.Torque_opengl_3.setParent(None)
        self.Torque_opengl_3.deleteLater()

        # 確保新的 widget 被正確加入佈局
        self.TorqueV_layout_3.addWidget(self.opengl_widget_temp)

        # 調整佈局的比例，確保新的 widget 佔據合適空間
        self.TorqueV_layout_3.setStretch(1, 20)  # 根據你的 UI 結構調整


    def signal_binding(self):  
        """綁定信號與槽函數"""
        self.sigUpdateOpenglWidget.connect(self.opengl_widget_instance_1.update_points) # 信號綁定 3D圖
        self.sigUpdateBending.connect(self.opengl_widget_bending.set_data) # 信號綁定 Bending
        self.sigUpdateTension.connect(self.opengl_widget_tension.set_data) # 信號綁定 Tension
        self.sigUpdateTorque.connect(self.opengl_widget_torque.set_data) # 信號綁定 Torque
        self.sigUpdateTemp.connect(self.opengl_widget_temp.set_data) # 信號綁定 Temp 溫度
        self.sigUpdateSafetyStatus.connect(self.bottom_frame.set_status)

    def tooltip_binding_up(self):
        self.tooltip_label_up = Ui_Tooltip_label_up(self)  # 建立獨立的 Tooltip

        MachRadar_VerP_authorize, MachRadar_VerP_authorize_info, sentinel_machradar_pro_status = get_license_data()
        self.button_tooltips_up = {
            "Image_machradar":  f"MachRadarPro: {get_mvc_version()}" + "\n"+
                                f"授權到期日: {MachRadar_VerP_authorize_info}"+"\n"+
                                f"語言: V1",
        }
        self.tooltips_buttons_up = {}  # 存放所有按鈕

        for btn_name, tooltip_text in self.button_tooltips_up.items():
            btn = self.findChild(QPushButton, btn_name)
            if btn:
                self.tooltips_buttons_up[btn] = tooltip_text
                btn.installEventFilter(self)  # 安裝事件過濾器

    def tooltip_binding(self):
        self.tooltip_label = Ui_Tooltip_label(self)  # 建立獨立的 Tooltip
        # 定義按鈕名稱與對應 Tooltip 文字
        self.button_tooltips = {
            "Btn1_Tare": "校正",
            "Btn2_BGMode": "Fx,Fy/加速度",
            "Btn3_PlotMode": "點圖/線圖",
            "Btn4_ResetAngle": "回到XY平面",
            "Btn5_cnc_Link": "CNC 連接",
            "Btn6_angle1": "儲存平面角度1\n點擊以儲存或切換3D平面角度。\n長按以取消儲存。",
            "Btn7_sleep_mode": "睡眠模式",
            "Btn8_target": "標靶",
            "Btn_CO2": "碳監測",
        }
        self.tooltips_buttons = {}  # 存放所有按鈕

        for btn_name, tooltip_text in self.button_tooltips.items():
            btn = self.findChild(QPushButton, btn_name)
            if btn:
                self.tooltips_buttons[btn] = tooltip_text
                btn.installEventFilter(self)  # 安裝事件過濾器
        
        # 定義標籤名稱與對應 Tooltip 文字
        self.label_tooltips = {
            "WIFI": "WiFi",
            "Battery": "電池",
            #TODO: 材質目前沒納入CO2計算，需再確認公式是否會用到
            "CO2_Pb_label": "根據ISO 513規範，\n硬質切削材料的分類及應用。",
            "CO2_EF_label": "根據能源局發布的係數，\n 完成ISO 14064-1、ISO 14064-2和ISO 14067的碳排放清單。"
        }
        self.tooltips_labels = {} # 存放所有標籤

        for label_name, tooltip_text in self.label_tooltips.items():
            label = self.findChild(QLabel, label_name)
            if label:
                self.tooltips_labels[label] = tooltip_text
                label.installEventFilter(self)  # 安裝事件過濾器

    def tab_binding(self):
                # 設定按鈕的列表
        self.tab_buttons = [
            self.Btn_Setting,
            self.Btn_Filter,
            self.Btn_View,
            self.Btn_Event,
            self.Btn_Mode,
            self.Btn_RecordScript,
            self.Btn_Help
        ]
        self.tab_current_index = 0  # 目前選取的按鈕索引

        # 設定焦點策略，確保窗口可以接收焦點
        self.setFocusPolicy(Qt.StrongFocus)
        # 將焦點設定給第一個按鈕並更新樣式
        self.tab_buttons[self.tab_current_index].setFocus()
        self.update_button_styles()

        # 連接按鈕的焦點變更信號
        for btn in self.tab_buttons:
            btn.setFocusPolicy(Qt.StrongFocus)
            btn.installEventFilter(self)

    def start_timer(self):
        """啟動計時器"""
        if self.timer_id is None:  # 確保計時器沒有重複啟動
            self.timer_id = self.startTimer(100)  # 每秒觸發一次
            logger.debug("計時器運行中...")

    def stop_timer(self):
        """停止計時器"""
        if self.timer_id:
            self.killTimer(self.timer_id)
            self.timer_id = None
            logger.debug("計時器已停止")

    def timerEvent(self, event):
        """計時器觸發時執行的函數"""
        if event.timerId() == self.timer_id:
            self.opengl_widget_bending.redraw()
            self.opengl_widget_tension.redraw()
            self.opengl_widget_torque.redraw()
            self.opengl_widget_temp.redraw()
            # logger.debug(f"計時器運行中: {self.count} 秒")

    def show_Toollist_window(self, toollist):
        # 刀把資料
        self.tool_list = toollist  # 保存工具列表到實例變數

        """顯示 Toollist 視窗，確保只建立一次"""
        # 如果視窗已存在且有效，先關閉它
        if self.Toollist_window is not None:
            try:
                self.Toollist_window.close()
                self.Toollist_window.deleteLater()
            except:
                pass
            self.Toollist_window = None

        # 創建新視窗
        self.Toollist_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
        self.ui_Toollist_window = Ui_ToolList_Window()
        self.ui_Toollist_window.setupUi(self.Toollist_window)
        
        # 判斷有沒有資料
        if len(self.tool_list) != 0:
            for tool in self.tool_list:
                self.ui_Toollist_window.addToolItem(
                    tool["toolname"], str(tool["id"])
                )
                # 更新工具設置
                tool_dict = dict(tool)  # 轉換為字典
                self.ui_Toollist_window.updateToolSettings(
                    str(tool["id"]), tool_dict
                )

            # 動態更新連接狀態
            count = 0
            for tool in self.tool_list:
                count += 1
                tool_id = tool["id"]
                try:
                    setting_btn = getattr(
                        self.ui_Toollist_window, f"Tool_setting_Btn_{tool_id}", None
                    )
                    link_btn = getattr(
                        self.ui_Toollist_window, f"Tool_link_Btn_{tool_id}", None
                    )
                    if setting_btn:  # 確保按鈕存在
                        tool_data = dict(tool)  # 確保每個按鈕綁定的是獨立的資料
                        setting_btn.clicked.connect(
                            partial(self.show_Toolinfo_window, tool_data)
                        )
                        setting_btn.clicked.connect(partial(lambda: self.close_window("Toollist_window"))) # 確保畫面上只有一個 popup 視窗
                    if link_btn:  # 確保按鈕存在
                        tool_data = dict(tool)  # 確保每個按鈕綁定的是獨立的資料
                        link_btn.clicked.connect(partial(self.eventLinkBtn, tool_data))
                        # 初始時禁用連結按鈕
                        link_btn.setEnabled(False)

                    # 比對 toolmac，若相符則變更顏色
                    if tool["toolmac"] == self.connect_mac:
                        tool_name_label = getattr(self.ui_Toollist_window, f"toolname_label_{tool_id}", None)
                        if tool_name_label:
                            tool_name_label.setStyleSheet("color:#7AFEC6;")
                except Exception as e:
                    logger.error(f"設置工具按鈕時發生錯誤: {e}")
                    continue
                    
        # 新增刀把
        self.ui_Toollist_window.add_tool_btn.clicked.connect(self.show_add_tool_window)

        # 設定為彈出視窗，點擊外部時自動關閉
        self.Toollist_window.setWindowFlags(Qt.Popup)
        # 安裝事件過濾器，監聽關閉事件
        self.Toollist_window.installEventFilter(self)

        # 取得按鈕的全域座標
        btn_pos = self.Btn_Toolname.mapToGlobal(QPoint(0, self.Btn_Toolname.height()))

        apply_scaling_to_window(self.Toollist_window, 350, 270, parent=self)
        # 設定 Toollist_window 的位置
        self.Toollist_window.move(btn_pos)        

    def updateConnect(self, found_devices):
        """搜尋網路設備並更新狀態"""
        try:
            # 確保 Toollist_window 和 ui_Toollist_window 都存在
            if not hasattr(self, 'Toollist_window') or self.Toollist_window is None:
                return
            if not hasattr(self, 'ui_Toollist_window'):
                return

            # 更新工具狀態
            for tool_id, tool_data in self.ui_Toollist_window.tool_data.items():
                try:
                    tool_mac = tool_data.settings.get('toolmac')
                    if tool_mac:
                        is_online = tool_mac in found_devices
                        if is_online:
                            tool_data.settings['toolip'] = found_devices[tool_mac]
                        self.ui_Toollist_window.updateConnectionStatus(tool_id, is_online)
                        
                        # 更新連結按鈕狀態
                        link_btn = getattr(self.ui_Toollist_window, f"Tool_link_Btn_{tool_id}", None)
                        if link_btn:
                            link_btn.setEnabled(is_online)
                except Exception as e:
                    logger.error(f"更新工具 {tool_id} 狀態時發生錯誤: {e}")
                    continue
                
        except Exception as e:
            logger.error(f"更新連接狀態時發生錯誤: {e}")
            # 如果發生錯誤，將所有工具設為離線
            if hasattr(self, 'ui_Toollist_window'):
                for tool_id in self.ui_Toollist_window.tool_data:
                    try:
                        self.ui_Toollist_window.updateConnectionStatus(tool_id, False)
                        # 禁用連結按鈕
                        link_btn = getattr(self.ui_Toollist_window, f"Tool_link_Btn_{tool_id}", None)
                        if link_btn:
                            link_btn.setEnabled(False)
                    except:
                        pass

    def show_add_tool_window(self):
        """顯示新增刀把視窗"""
        if not hasattr(self, "AddTool_window") or self.AddTool_window is None:
            self.AddTool_window = QWidget(self)
            self.ui_AddTool_window = Ui_add_tool_window()
            self.ui_AddTool_window.setupUi(self.AddTool_window)

            # self.ui_AddTool_window.add_button.clicked.connect(self.add_tool)
            self.AddTool_window.setWindowFlags(Qt.Popup)
            self.AddTool_window.installEventFilter(self)

            self.ui_AddTool_window.mac_lineEdit.setText("") 
            
            # 修改儲存按鈕的連接
            self.ui_AddTool_window.add_save_Btn.clicked.connect(lambda: self.add_tool(
                self.ui_AddTool_window.mac_lineEdit.text(),
            ))
            
            self.ui_AddTool_window.add_cancel_Btn.clicked.connect(partial(lambda: self.close_window("AddTool_window")))

        apply_scaling_to_window(self.AddTool_window, 399, 59, parent=self)

    def add_tool(self, mac_address):
        """處理新增刀把的邏輯"""
        
        # 先檢查資料庫
        found, device_info = check_mac_in_database(mac_address)
        if found:
            self.show_Remind_window("此 MAC 地址已存在於資料庫中")
            return
        
        # 如果資料庫中沒有，則進行爬蟲檢查
        found, device_info = check_mac_in_network(
            mac_address=mac_address,
        )
        
        if found:
            # 如果找到裝置，關閉視窗
            self.close_window("AddTool_window")
            self.show_Remind_window("成功新增裝置")
            # 發送信號要求更新工具列表
            self.sigUpdateToolList.emit()
        else:
            # 如果沒找到裝置，顯示提示訊息
            self.show_Remind_window("未找到指定的 MAC 地址裝置")

    def show_Toolinfo_window(self, data):

        # print(data)
        tool_data = data
        # print(tool_data)
        logger.debug("Pressed Settings")

        """顯示 Settings 設定視窗"""
        if not hasattr(self, "Toolinfo_window") or self.Toolinfo_window is None:
            self.Toolinfo_window = QWidget(self) # 獨立於 Toollist_window
            self.ui_Toolinfo_window = Ui_ToolInfo_Window()
            self.ui_Toolinfo_window.setupUi(self.Toolinfo_window)

            # 刀把資訊
            self.ui_Toolinfo_window.toolinfoName_lineEdit.setText(tool_data["toolname"])
            self.ui_Toolinfo_window.toolinfoIP_lineEdit.setText(tool_data["toolip"])
            self.ui_Toolinfo_window.toolinfoFrequency_lineEdit.setText(
                str(tool_data["sample_rate"])
            )

            # 校正係數
            self.ui_Toolinfo_window.Tareinfo_Fx_lineedit.setText(
                str(tool_data["tare_xv"])
            )
            self.ui_Toolinfo_window.Tareinfo_Fy_lineedit.setText(
                str(tool_data["tare_yv"])
            )
            self.ui_Toolinfo_window.Tareinfo_Fz_lineedit.setText(
                str(tool_data["tare_zv"])
            )
            self.ui_Toolinfo_window.Tareinfo_T_lineedit.setText(
                str(tool_data["tare_tv"])
            )

            # 換算係數
            self.ui_Toolinfo_window.conversion_Fx_lineEdit.setText(
                str(tool_data["Linear_x"])
            )
            self.ui_Toolinfo_window.conversion_Fy_lineEdit.setText(
                str(tool_data["Linear_y"])
            )
            self.ui_Toolinfo_window.conversion_Fz_lineEdit.setText(
                str(tool_data["Linear_z"])
            )
            self.ui_Toolinfo_window.conversion_T_lineEdit.setText(
                str(tool_data["Linear_t"])
            )

            # 轉換單位 mm
            kl_mm = tool_data["Kl"] * 1000
            self.ui_Toolinfo_window.conversion_TOL_lineEdit.setText(str(kl_mm))

            # 綁定錄製設定按鈕點擊事件
            self.ui_Toolinfo_window.Recsetting_btn.clicked.connect(partial(lambda: self.show_RecordSetting_window(tool_data)))
            self.ui_Toolinfo_window.Recsetting_btn.clicked.connect(partial(lambda: self.close_window("Toolinfo_window"))) # 確保畫面上只有一個 popup 視窗

            # 🔥 添加輸入驗證器 - 限制可修改的參數只能輸入數字
            # 校正係數輸入框（允許浮點數）
            tare_lineEdits = [
                "Tareinfo_Fx_lineedit", "Tareinfo_Fy_lineedit", 
                "Tareinfo_Fz_lineedit", "Tareinfo_T_lineedit"
            ]
            for tare_edit in tare_lineEdits:
                line_edit_obj = getattr(self.ui_Toolinfo_window, tare_edit)
                InputValidator.apply_validator(line_edit_obj, "float")

            # 換算係數輸入框（允許浮點數）
            conversion_lineEdits = [
                "conversion_Fx_lineEdit", "conversion_Fy_lineEdit",
                "conversion_Fz_lineEdit", "conversion_T_lineEdit", "conversion_TOL_lineEdit"
            ]
            for conversion_edit in conversion_lineEdits:
                line_edit_obj = getattr(self.ui_Toolinfo_window, conversion_edit)
                InputValidator.apply_validator(line_edit_obj, "float")

            # 設定為彈出視窗（點擊外部時自動關閉）
            self.Toolinfo_window.setWindowFlags(Qt.Popup)

            # 安裝事件過濾器，監聽關閉事件
            self.Toolinfo_window.installEventFilter(self)

            # 綁定刀把系統檢查按鈕的點擊事件
            self.ui_Toolinfo_window.Tool_system_check.clicked.connect(partial(lambda: self.sigDoSystemCheck.emit(tool_data)))

            # 綁定校正按鈕的點擊事件
            self.ui_Toolinfo_window.Tool_tare_btn.clicked.connect(partial(lambda: self.toolinfo_tare(tool_data)))

            # 綁定儲存按鈕的點擊事件
            self.ui_Toolinfo_window.Toolinfo_save_btn.clicked.connect(partial(self.save_toolinfo, tool_data))
            self.ui_Toolinfo_window.Toolinfo_save_btn.clicked.connect(partial(lambda: self.close_window("Toolinfo_window")))

            # 綁定關閉按鈕的點擊事件
            self.ui_Toolinfo_window.Toolinfo_close_btn.clicked.connect(partial(lambda: self.close_window("Toolinfo_window")))

        # 根據連線狀態設定按鈕 enabled/disabled
        if self.connect_mac == tool_data["toolmac"]:
            self.ui_Toolinfo_window.Tool_tare_btn.setEnabled(True)
            self.ui_Toolinfo_window.Tool_system_check.setEnabled(True)
        else:
            self.ui_Toolinfo_window.Tool_tare_btn.setEnabled(False)
            self.ui_Toolinfo_window.Tool_system_check.setEnabled(False)

        apply_scaling_to_window(self.Toolinfo_window, 450, 690, parent=self)
        # self.ToolInfo_Window.repaint()


    def show_RecordSetting_window(self, tool_data):
        """顯示錄製設定視窗"""
        if not hasattr(self, "RecordSetting_window") or self.RecordSetting_window is None:
            self.RecordSetting_window = QWidget(self)  # 獨立於 Toolinfo_window
            self.ui_RecordSetting_window = Ui_RecordSetting_window()
            self.ui_RecordSetting_window.setupUi(self.RecordSetting_window)

            # 載入現有的自動錄製設定
            self.load_auto_recording_settings(tool_data)
            
            # 根據現有設定更新 UI 元件的 enabled/disabled
            self.update_RecordSetting_window(tool_data)

            # 綁定checkbox的點擊事件 (更新enabled/disabled)
            self.ui_RecordSetting_window.auto_record_checkBox.clicked.connect(partial(self.update_RecordSetting_window, tool_data))
            self.ui_RecordSetting_window.cf_enabled_checkBox.clicked.connect(partial(self.update_RecordSetting_window, tool_data))
            self.ui_RecordSetting_window.fz_enabled_checkBox.clicked.connect(partial(self.update_RecordSetting_window, tool_data))
            self.ui_RecordSetting_window.t_enabled_checkbox.clicked.connect(partial(self.update_RecordSetting_window, tool_data))

            # 綁定儲存按鈕的點擊事件
            self.ui_RecordSetting_window.RecordSetting_save_btn.clicked.connect(
                partial(self.save_auto_recording_settings, tool_data)
            )
            self.ui_RecordSetting_window.RecordSetting_save_btn.clicked.connect(
                partial(lambda: self.close_window("RecordSetting_window"))
            )

            # 綁定關閉按鈕的點擊事件
            self.ui_RecordSetting_window.RecordSetting_close_btn.clicked.connect(
                partial(lambda: self.close_window("RecordSetting_window"))
            )
            # 關閉 RecordSetting 後再次顯示 Toolinfo 視窗
            self.ui_RecordSetting_window.RecordSetting_close_btn.clicked.connect(
                partial(lambda: self.show_Toolinfo_window(tool_data))
            )

            # 添加輸入驗證器
            # 錄製/預錄秒數輸入框（整數）
            seconds_lineEdits = [
                "record_seconds_lineEdit", "pre_record_seconds_lineEdit"
            ]
            for seconds_edit in seconds_lineEdits:
                line_edit_obj = getattr(self.ui_RecordSetting_window, seconds_edit)
                InputValidator.apply_validator(line_edit_obj, "int")

            # Threshold 輸入框（浮點數）
            threshold_lineEdits = [
                "cf_threshold_lineEdit", "fz_threshold_lineEdit", "t_threshold_lineEdit"
            ]
            for threshold_edit in threshold_lineEdits:
                line_edit_obj = getattr(self.ui_RecordSetting_window, threshold_edit)
                InputValidator.apply_validator(line_edit_obj, "float")

            # 設定為彈出視窗
            self.RecordSetting_window.setWindowFlags(Qt.Popup)

            # 安裝事件過濾器，監聽關閉事件
            self.RecordSetting_window.installEventFilter(self)

        apply_scaling_to_window(self.RecordSetting_window, 900, 635, parent=self)

    def load_auto_recording_settings(self, tool_data):
        """載入自動錄製設定到UI"""
        # 從工具資料中取得自動錄製設定 (使用資料庫欄位名稱)
        auto_record_enabled = tool_data.get("auto_record_enabled", False)
        auto_max_record_count = tool_data.get("auto_max_record_count", 100)
        auto_record_seconds = tool_data.get("auto_record_seconds", 10)
        auto_pre_record_seconds = tool_data.get("auto_pre_record_seconds", 0)
        auto_cf_enabled = tool_data.get("auto_cf_enabled", False)
        auto_cf_threshold = tool_data.get("auto_cf_threshold", 0)
        auto_fz_enabled = tool_data.get("auto_fz_enabled", False)
        auto_fz_threshold = tool_data.get("auto_fz_threshold", 0)
        auto_t_enabled = tool_data.get("auto_t_enabled", False)
        auto_t_threshold = tool_data.get("auto_t_threshold", 0)

        # 將 max_count 轉換為 combo box index
        max_record_count_index = 0  # 預設 100筆
        if auto_max_record_count == 500:
            max_record_count_index = 1
        elif auto_max_record_count == 1000:
            max_record_count_index = 2

        # 設定UI元件
        self.ui_RecordSetting_window.auto_record_checkBox.setChecked(auto_record_enabled)
        self.ui_RecordSetting_window.max_auto_record_count_comboBox.setCurrentIndex(max_record_count_index)
        self.ui_RecordSetting_window.record_seconds_lineEdit.setText(str(auto_record_seconds))
        self.ui_RecordSetting_window.pre_record_seconds_lineEdit.setText(str(auto_pre_record_seconds))
        self.ui_RecordSetting_window.cf_enabled_checkBox.setChecked(auto_cf_enabled)
        self.ui_RecordSetting_window.cf_threshold_lineEdit.setText(str(auto_cf_threshold))
        self.ui_RecordSetting_window.fz_enabled_checkBox.setChecked(auto_fz_enabled)
        self.ui_RecordSetting_window.fz_threshold_lineEdit.setText(str(auto_fz_threshold))
        self.ui_RecordSetting_window.t_enabled_checkbox.setChecked(auto_t_enabled)
        self.ui_RecordSetting_window.t_threshold_lineEdit.setText(str(auto_t_threshold))

    def save_auto_recording_settings(self, tool_data):
        """儲存自動錄製設定"""
        
        # 🔥 儲存前先驗證所有自動錄製設定欄位
        is_valid, error_message = InputValidator.validate_auto_recording_fields(self.ui_RecordSetting_window)
        if not is_valid:
            # 驗證失敗，顯示錯誤訊息
            logger.error(f"自動錄製設定欄位驗證失敗: {error_message}")
            self.show_Remind_window(error_message)
            return  # 停止執行儲存
        
        # 驗證成功，繼續原有的儲存邏輯
        logger.info("自動錄製設定欄位驗證成功，開始儲存")
        
        # 從UI取得設定值
        auto_record_enabled = self.ui_RecordSetting_window.auto_record_checkBox.isChecked()
        max_record_count_index = self.ui_RecordSetting_window.max_auto_record_count_comboBox.currentIndex()
        auto_record_seconds = int(self.ui_RecordSetting_window.record_seconds_lineEdit.text() or 10)
        auto_pre_record_seconds = int(self.ui_RecordSetting_window.pre_record_seconds_lineEdit.text() or 0)
        auto_cf_enabled = self.ui_RecordSetting_window.cf_enabled_checkBox.isChecked()
        auto_cf_threshold = float(self.ui_RecordSetting_window.cf_threshold_lineEdit.text() or 0)
        auto_fz_enabled = self.ui_RecordSetting_window.fz_enabled_checkBox.isChecked()
        auto_fz_threshold = float(self.ui_RecordSetting_window.fz_threshold_lineEdit.text() or 0)
        auto_t_enabled = self.ui_RecordSetting_window.t_enabled_checkbox.isChecked()
        auto_t_threshold = float(self.ui_RecordSetting_window.t_threshold_lineEdit.text() or 0)

        # 將 combo box index 轉換為實際的 max_count 值
        auto_record_max_count = 100  # 預設 100筆
        if max_record_count_index == 1:
            auto_record_max_count = 500
        elif max_record_count_index == 2:
            auto_record_max_count = 1000

        # 更新工具資料 (使用資料庫欄位名稱)
        tool_data.update({
            "auto_record_enabled": auto_record_enabled,
            "auto_max_record_count": auto_record_max_count,
            "auto_record_seconds": auto_record_seconds,
            "auto_pre_record_seconds": auto_pre_record_seconds,
            "auto_cf_enabled": auto_cf_enabled,
            "auto_cf_threshold": auto_cf_threshold,
            "auto_fz_enabled": auto_fz_enabled,
            "auto_fz_threshold": auto_fz_threshold,
            "auto_t_enabled": auto_t_enabled,
            "auto_t_threshold": auto_t_threshold,
        })

        # 發送信號給controller更新資料庫
        self.sigSaveAutoRecordingSettings.emit(tool_data)

        logger.info(f"Auto recording settings saved for tool {tool_data.get('toolname', 'Unknown')}")
    
    def update_RecordSetting_window(self, tool_data):
        """更新錄製設定視窗 (enabled/disabled)"""
        
        # 取得checkbox的狀態
        auto_record_enabled = self.ui_RecordSetting_window.auto_record_checkBox.isChecked()
        auto_cf_enabled = self.ui_RecordSetting_window.cf_enabled_checkBox.isChecked()
        auto_fz_enabled = self.ui_RecordSetting_window.fz_enabled_checkBox.isChecked()
        auto_t_enabled = self.ui_RecordSetting_window.t_enabled_checkbox.isChecked()

        # 設定 enabled/disabled e.g. 自動錄製關閉時，所屬的設定是 disabled 的
        self.ui_RecordSetting_window.auto_group.setEnabled(auto_record_enabled)
        self.ui_RecordSetting_window.cf_threshold_lineEdit.setEnabled(auto_cf_enabled)
        self.ui_RecordSetting_window.fz_threshold_lineEdit.setEnabled(auto_fz_enabled)
        self.ui_RecordSetting_window.t_threshold_lineEdit.setEnabled(auto_t_enabled)


    def show_System_check_window(self, tool_data):
        """刀把系統檢查(View)：顯示確認、進行中的視窗"""

        # 顯示確認視窗
        result = self.show_Confirm_Window("*刀把檢查*\n請保持刀把靜止10秒，以檢測穩定的數據。")

        # 使用者按下取消
        if result != QDialog.Accepted:
            logger.info("system check rejected")
            return
        logger.info("system check accepted")
        logger.info(f"tool_data: {tool_data}")

        # 顯示系統檢查進行中的視窗
        self.show_pop_up_window("連線成功!\n系統檢查中...")

        # 每秒更新進行中視窗的秒數
        def update_remaining_seconds(remaining_seconds: int):
            logger.debug(f"update remaining seconds = {remaining_seconds}")
            if hasattr(self, 'pop_up_dialog') and self.pop_up_dialog is not None:
                self.pop_up_dialog.update_label(f"剩餘接收時間: {remaining_seconds} 秒")
                if remaining_seconds > 0:
                    QTimer.singleShot(1000, partial(update_remaining_seconds, remaining_seconds-1))
                else:
                    self.close_hint_window()
        QTimer.singleShot(1000, partial(update_remaining_seconds, 10))

    
    def toolinfo_tare(self, tool_data):
        """Toolinfo_window 的刀把校正"""

        if self.connect_mac != tool_data["toolmac"]:
            self.show_Remind_window("請先連線到刀把")
            return
        
        self.sigDoTare.emit() # 發送信號給 controller 的 Tare_holder_window 執行


    def save_toolinfo(self, tool_data_old: dict):
        """儲存刀把資料"""
        
        # 確認 ui_Toolinfo_Window 是否存在
        if not hasattr(self, "ui_Toolinfo_window") or self.ui_Toolinfo_window is None:
            logger.error("ui_Toolinfo_window is not found")
            return

        # 確認 ToolInfo_Window 是否存在
        if not hasattr(self, "Toolinfo_window") or self.Toolinfo_window is None:
            logger.error("Toolinfo_window is not found")
            return
        
        # 🔥 儲存前先驗證刀把資訊欄位
        is_valid, error_message = InputValidator.validate_toolinfo_fields(self.ui_Toolinfo_window)
        if not is_valid:
            # 驗證失敗，顯示錯誤訊息
            logger.error(f"刀把資訊驗證失敗: {error_message}")
            self.show_Remind_window(error_message)
            return  # 不繼續執行儲存
        
        # 驗證成功，繼續原有的儲存邏輯
        logger.info("刀把資訊驗證成功，開始儲存")
        
        # 從 ui 取得資料，從 str 轉換成正確型別
        tool_data_new = dict(tool_data_old)
        tool_data_new["toolname"] = self.ui_Toolinfo_window.toolinfoName_lineEdit.text()
        tool_data_new["toolip"] = self.ui_Toolinfo_window.toolinfoIP_lineEdit.text()
        tool_data_new["sample_rate"] = int(self.ui_Toolinfo_window.toolinfoFrequency_lineEdit.text())
        #
        tool_data_new["tare_xv"] = float(self.ui_Toolinfo_window.Tareinfo_Fx_lineedit.text())
        tool_data_new["tare_yv"] = float(self.ui_Toolinfo_window.Tareinfo_Fy_lineedit.text())
        tool_data_new["tare_zv"] = float(self.ui_Toolinfo_window.Tareinfo_Fz_lineedit.text())
        tool_data_new["tare_tv"] = float(self.ui_Toolinfo_window.Tareinfo_T_lineedit.text())
        #
        tool_data_new["Linear_x"] = float(self.ui_Toolinfo_window.conversion_Fx_lineEdit.text())
        tool_data_new["Linear_y"] = float(self.ui_Toolinfo_window.conversion_Fy_lineEdit.text())
        tool_data_new["Linear_z"] = float(self.ui_Toolinfo_window.conversion_Fz_lineEdit.text())
        tool_data_new["Linear_t"] = float(self.ui_Toolinfo_window.conversion_T_lineEdit.text())
        tool_data_new["Kl"] = float(self.ui_Toolinfo_window.conversion_TOL_lineEdit.text()) / 1000
        
        # 發送信號給 controller 存檔
        self.sigSaveToolInfo.emit(tool_data_new)

        # 更新主畫面的刀把名稱
        if self.connect_mac == tool_data_old["toolmac"]:
            self.Btn_Toolname.setText(
                QCoreApplication.translate("main_window", tool_data_new["toolname"], None)
            )


    def show_Setting_window(self, directory, filename, save_raw_data):
        logger.info(f"存檔目錄: {directory}")
        logger.info(f"存檔名稱: {filename}")
        logger.info(f"存原始數據與否: {save_raw_data}")
       
        """顯示 Setting 視窗，確保只建立一次"""
        if self.Setting_window is None:
            self.Setting_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_Setting_window = Ui_Setting_Window()
            self.ui_Setting_window.setupUi(self.Setting_window)

            # 檔案目錄
            self.ui_Setting_window.FolderPath_lineEdit.setText(str(directory))
            # TODO: 這是動態檔案名稱 要再調整，要串記錄檔存檔位置
            
            self.ui_Setting_window.txtname_lineEdit.setText(str(filename))

            # 設定儲存原始數據 (raw data) checkbox 狀態
            self.ui_Setting_window.save_rawdata_checkBox.setChecked(save_raw_data)
            
            # 設定為彈出視窗，點擊外部時自動關閉
            self.Setting_window.setWindowFlags(Qt.Popup)

            # 安裝事件過濾器，監聽關閉事件
            self.Setting_window.installEventFilter(self)

            # 設定 msra 檔名刷新按鈕
            self.ui_Setting_window.Setting_AutoFileName_btn.clicked.connect(
                self.reset_text_file_name
            )

            # 綁定按鈕的點擊事件
            self.ui_Setting_window.Setting_close_btn.clicked.connect(partial(lambda: self.close_window("Setting_window")))
            self.ui_Setting_window.Setting_save_btn.clicked.connect(self.set_text_file_name)

            

        apply_scaling_to_window(self.Setting_window, 500, 165, parent=self)

    def show_Filter_window(self):
        """顯示 Filter 視窗，確保只建立一次"""
        if self.Filter_window is None:
            self.Filter_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_Filter_window = Ui_Filter_Window()
            self.ui_Filter_window.setupUi(self.Filter_window)

            self.filter_comparer = DataComparer(self.filter_data)
            self.filter_comparer_temp = self.filter_comparer.begin_edit()

            # 信號設定
            # radio button toggled 
            self.ui_Filter_window.Nofilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.Nofilter_radio))
            self.ui_Filter_window.Meanfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.Meanfilter_radio))
            self.ui_Filter_window.Medianfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.Medianfilter_radio))
            self.ui_Filter_window.Gaussianfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.Gaussianfilter_radio))
            self.ui_Filter_window.MSfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.MSfilter_radio))
            self.ui_Filter_window.Lowfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.Lowfilter_radio))
            self.ui_Filter_window.Highfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.Highfilter_radio))
            self.ui_Filter_window.SGfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.SGfilter_radio))
            self.ui_Filter_window.MAfilter_radio.toggled.connect(lambda: self.on_filter_radio_toggled(self.ui_Filter_window.MAfilter_radio))

            self.ui_Filter_window.filter_Slider.valueChanged.connect(self.on_slider_value_changed)

            # TAG: 把濾波資料帶入 Filter_window

            getattr(self.ui_Filter_window, self.filter_data['filter_type']).setChecked(True)

            self.ui_Filter_window.filter_Slider.setValue(self.filter_data['filter_values'])

            filters_lineEdit = ['Lowfilter', 'Highfilter', 'SGfilter', 'MAfilter']

            for filter_name in filters_lineEdit:
                line_edit = getattr(self.ui_Filter_window, f"{filter_name}_lineEdit")
                line_edit.setText(str(self.filter_data[f"{filter_name}_edit"]))
                InputValidator.apply_validator(line_edit, "int", max_length=4)

            # 綁定儲存按鈕的點擊事件
            self.ui_Filter_window.filter_save_btn.clicked.connect(self.filterSaveBtn) 


            # 設定為彈出視窗，點擊外部時自動關閉
            self.Filter_window.setWindowFlags(Qt.Popup)
            # 安裝事件過濾器，監聽關閉事件
            self.Filter_window.installEventFilter(self)

            # 綁定關閉按鈕的點擊事件
            self.ui_Filter_window.filter_close_btn.clicked.connect(partial(lambda: self.close_window("Filter_window")))
            # # 綁定儲存按鈕的點擊事件
            # self.ui_Filter_window.filter_save_btn.clicked.connect(
            #     partial(self.show_Comfirm_Window, "是否儲存已更改的濾波器269？")
            # )

        apply_scaling_to_window(self.Filter_window, 330, 450, parent=self)

    # TAG: View 視窗顯示
    def show_View_window(self):
        """顯示 View 視窗，確保只建立一次"""
        if self.View_window is None:
            self.View_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_View_window = Ui_View_Window()
            self.ui_View_window.setupUi(self.View_window)

            self.view_setting_comparer = DataComparer(self.view_setting_data)
            self.view_setting_comparer_temp = self.view_setting_comparer.begin_edit()

            # 設定 初始化值
            self.view_setting_value()

            # 監聽 QCheckBox 狀態變化
            self.ui_View_window.CF_Auto_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.CF_Auto_checkBox))
            self.ui_View_window.FxFy_Auto_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.FxFy_Auto_checkBox))
            self.ui_View_window.Fz_Auto_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.Fz_Auto_checkBox))
            self.ui_View_window.Torque_Auto_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.Torque_Auto_checkBox))
            self.ui_View_window.Temp_Auto_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.Temp_Auto_checkBox))
            self.ui_View_window.view_OpenAW_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.view_OpenAW_checkBox))
            self.ui_View_window.view_HideRing_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.view_HideRing_checkBox))
            self.ui_View_window.view2_HideScale_checkBox.stateChanged.connect(lambda: self.view_check_box(self.ui_View_window.view2_HideScale_checkBox))

            # 綁定radio_4按鈕的點擊事件
            self.ui_View_window.ColorSpectrum_radio.toggled.connect(
                lambda checked: self.on_view_radio_toggled(self.ui_View_window.ColorSpectrum_radio, checked)
            )
            self.ui_View_window.ColorSpectrum_radio_2.toggled.connect(
                lambda checked: self.on_view_radio_toggled(self.ui_View_window.ColorSpectrum_radio_2, checked)
            )
            self.ui_View_window.ColorSpectrum_radio_3.toggled.connect(
                lambda checked: self.on_view_radio_toggled(self.ui_View_window.ColorSpectrum_radio_3, checked)
            )
            self.ui_View_window.ColorSpectrum_radio_4.toggled.connect(
                lambda checked: self.on_view_radio_toggled(self.ui_View_window.ColorSpectrum_radio_4, checked)
            )
            


            # # 綁定radio_4按鈕的點擊事件
            # self.ui_View_window.ColorSpectrum_radio_4.clicked.connect(
            #     partial(
            #         self.show_Remind_window, "未設定刀刃數量，系統已默認為4刃刀。120"
            #     )
            # )

             # 綁定儲存按鈕的點擊事件
            self.ui_View_window.view2_save_btn.clicked.connect(self.viewSetWarningSaveBtn) 
            self.ui_View_window.view1_save_btn.clicked.connect(self.viewSaveWarningFileBtn) 
            self.ui_View_window.view1_read_btn.clicked.connect(self.viewReadWarningFileBtn) 

            # 設定為彈出視窗，點擊外部時自動關閉
            self.View_window.setWindowFlags(Qt.Popup)
            # 安裝事件過濾器，監聽關閉事件
            self.View_window.installEventFilter(self)


            # 綁定關閉按鈕的點擊事件
            self.ui_View_window.view2_close_btn.clicked.connect(partial(lambda: self.close_window("View_window")))
         

        apply_scaling_to_window(self.View_window, 500, 700, parent=self)

    # TODO: View存檔視窗
    def viewSaveWarningFileBtn(self):
        logger.info("view Save Button Clicked")
        logger.error(f"After: {self.view_setting_data}")
        # 複製設定出來
        self.before_view_setting_data = self.view_setting_data.copy()
        # 更新 CF_Auto_value
        self.before_view_setting_data["CF_Auto_value"]={
            "state": self.view_setting_data["CF_Auto_value"]["state"],
            "maximum": float(self.ui_View_window.CF_max_lineedit.text() or 0.0),
            "minimum": float(self.ui_View_window.CF_min_lineedit.text() or 0.0),
            "warning": float(self.ui_View_window.CF_warning_lineedit.text() or 0.0),
            "alarm": float(self.ui_View_window.CF_alarm_lineedit.text() or 0.0)
        }

        # 更新 FxFy_Auto_value
        self.before_view_setting_data["FxFy_Auto_value"]={
            "state": self.view_setting_data["FxFy_Auto_value"]["state"],
            "peak_plus": float(self.ui_View_window.FxFy_pos_peak_lineedit.text() or 0.0),
            "peak_minus": float(self.ui_View_window.FxFy_neg_peak_lineedit.text() or 0.0)
        }

        # 更新 Fz_Auto_value
        self.before_view_setting_data["Fz_Auto_value"]={
            "state": self.view_setting_data["Fz_Auto_value"]["state"],
            "peak_plus": float(self.ui_View_window.Fz_pos_peak_lineedit.text() or 0.0),
            "peak_minus": float(self.ui_View_window.Fz_neg_peak_lineedit.text() or 0.0),
            "alarm_plus": float(self.ui_View_window.Fz_pos_alarm_lineedit.text() or 0.0),
            "alarm_minus": float(self.ui_View_window.Fz_neg_alarm_lineedit.text() or 0.0),
            "warning_plus": float(self.ui_View_window.Fz_pos_warning_lineedit.text() or 0.0),
            "warning_minus": float(self.ui_View_window.Fz_neg_warning_lineedit.text() or 0.0)
        }

        # 更新 Torque_Auto_value
        self.before_view_setting_data["Torque_Auto_value"]={
            "state": self.view_setting_data["Torque_Auto_value"]["state"],
            "peak_plus": float(self.ui_View_window.Torque_pos_peak_lineedit.text() or 0.0),
            "peak_minus": float(self.ui_View_window.Torque_neg_peak_lineedit.text() or 0.0),
            "alarm_plus": float(self.ui_View_window.Torque_pos_alarm_lineedit.text() or 0.0),
            "alarm_minus": float(self.ui_View_window.Torque_neg_alarm_lineedit.text() or 0.0),
            "warning_plus": float(self.ui_View_window.Torque_pos_warning_lineedit.text() or 0.0),
            "warning_minus": float(self.ui_View_window.Torque_neg_warning_lineedit.text() or 0.0)
        }

        # 更新 Temp_Auto_value
        self.before_view_setting_data["Temp_Auto_value"]={
            "state": self.view_setting_data["Temp_Auto_value"]["state"],
            "maximum": float(self.ui_View_window.Temp_max_lineedit.text() or 0.0),
            "minimum": float(self.ui_View_window.Temp_min_lineedit.text() or 0.0),
            "warning": float(self.ui_View_window.Temp_warning_lineedit.text() or 0.0),
            "alarm": float(self.ui_View_window.Temp_alarm_lineedit.text() or 0.0)
        }

        # 關閉 View_window 只存在
        self.close_window("View_window")

        current_dir = os.path.abspath(os.getcwd())
        # directory = current_dir + "/ViewSetting"
        directory = os.path.join(current_dir, "ViewSetting")
        msrb_file_path = directory
        # 預設檔案名稱 View_Setting_20250715-143003.msrb
        msra_file_name = "View_Setting_" + time.strftime("%Y%m%d-%H%M%S",time.localtime()) + ".msrb"
        

        """顯示 Setting 視窗，確保只建立一次"""
        if self.Setting_window is None:
            self.Setting_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_Setting_window = Ui_Setting_Window()
            self.ui_Setting_window.setupUi(self.Setting_window)

            # 檔案目錄
            logger.debug(f"檔案目錄: {msrb_file_path}")
            self.ui_Setting_window.FolderPath_lineEdit.setText(str(msrb_file_path))
            # TODO: 這是動態檔案名稱 要再調整，要串記錄檔存檔位置
            
            self.ui_Setting_window.txtname_lineEdit.setText(str(msra_file_name))

            self.ui_Setting_window.save_rawdata_checkBox.hide()
            self.ui_Setting_window.Setting_AutoFileName_btn.hide()
            self.ui_Setting_window.txtname_lable.setText("msrb檔名 :")

            # 設定為彈出視窗，點擊外部時自動關閉
            self.Setting_window.setWindowFlags(Qt.Popup)

            # 安裝事件過濾器，監聽關閉事件
            self.Setting_window.installEventFilter(self)

            # 設定 msra 檔名刷新按鈕
            # self.ui_Setting_window.Setting_AutoFileName_btn.clicked.connect(
            #     self.reset_text_file_name
            # )

            # 綁定關閉按鈕的點擊事件
            self.ui_Setting_window.Setting_close_btn.clicked.connect(partial(lambda: self.close_window("Setting_window")))
            # 綁定儲存按鈕的點擊事件
            # self.ui_Setting_window.Setting_save_btn.clicked.connect(
            #     partial(self.show_Comfirm_Window, "是否儲存已更改的設定？146")
            # )
            self.ui_Setting_window.Setting_save_btn.clicked.connect(self.viewSaveWarningFile)
            # self.ui_Setting_window.Setting_save_btn.clicked.connect(self.viewSaveWarningFile)

            

        # 更新 Setting_window 的大小以確保可以正確獲取建議尺寸
        self.Setting_window.adjustSize()  # 確保視窗尺寸正確

        # 讓 Setting_Window 顯示在 MainWindow 正中央
        apply_scaling_to_window(self.Setting_window, 500, 165, parent=self)

    def viewSaveWarningFile(self):
        logger.error("view Read Button Clicked")

        msrb_file_path =self.ui_Setting_window.FolderPath_lineEdit.text()
        file_name = self.ui_Setting_window.txtname_lineEdit.text()

        self.view_setting_data = self.before_view_setting_data.copy()

        data = {
            "action": "save",
            "msrb_file_path": msrb_file_path,
            "file_name": file_name,
            "data": self.view_setting_data
        }
        self.sigViewRWFileData.emit(data)
        self.close_window("Setting_window")

    def viewReadWarningFileBtn(self):
        logger.error("view Read Button Clicked")
        data = {
            "action": "read",
            "data": self.view_setting_data
        }
        self.sigViewRWFileData.emit(data)
        # self.close_window("Setting_window")

    # def viewReadWarningFile(self):
    #     logger.info("view Read Button Clicked")
    #     self.close_window("Setting_window")

    def show_Event_window(self):
        """顯示 Event 視窗，確保只建立一次"""
        if self.Event_window is None:
            self.Event_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_Event_window = Ui_Event_window()
            self.ui_Event_window.setupUi(self.Event_window)

            # 綁定關閉按鈕的點擊事件
            self.ui_Event_window.close_button.clicked.connect(partial(lambda:self.close_window("Event_window")))

            # 設定為彈出視窗，點擊外部時自動關閉
            self.Event_window.setWindowFlags(Qt.Popup)

        apply_scaling_to_window(self.Event_window, 800, 600, parent=self)

    def show_ModeSwitch_window(self):
        """顯示 ModeSwitch 視窗，確保只建立一次"""
        if self.ModeSwitch_window is None:
            self.ModeSwitch_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_ModeSwitch_window = Ui_ModeSwitch_Window()
            self.ui_ModeSwitch_window.setupUi(self.ModeSwitch_window)

            # 設定為彈出視窗，點擊外部時自動關閉
            self.ModeSwitch_window.setWindowFlags(Qt.Popup)

        # 更新 ModeSwitch_window 的大小以確保可以正確獲取建議尺寸
        self.ModeSwitch_window.adjustSize()  # 確保視窗尺寸正確

        apply_scaling_to_window(self.ModeSwitch_window, 210, 360, parent=self)
        # 取得按鈕的全域座標
        btn_pos = self.Btn_Mode.mapToGlobal(QPoint(0, self.Btn_Mode.height()))

        # 設定 Toollist_window 的位置
        self.ModeSwitch_window.move(btn_pos)

        # 定義所有按鈕的對應關係
        self.button_icon_map = {
            "realtime_widget": "realtime_btnicon",
            "readfile_widget": "readfile_btnicon",
            "realtime_pro_widget": "realtimepro_btnicon",
            "readfile_pro_widget": "readfilepro_btnicon",
            "homepage_widget": "homepage_btnicon",
            "analyzer_widget": "analyzer_btnicon",
        }

        # 讀取按鈕並綁定事件
        self.modeswitch_buttons = {}  # 存放按鈕對應的 QWidget
        self.modeswitch_icons = {}  # 存放對應的 icon QPushButton

        for btn_name, icon_name in self.button_icon_map.items():
            btn = self.ModeSwitch_window.findChild(QWidget, btn_name)
            icon_btn = self.ModeSwitch_window.findChild(QPushButton, icon_name)
            if btn and icon_btn:  # 確保按鈕存在
                self.modeswitch_buttons[btn] = icon_btn
                icon_btn.setIcon(QIcon())  # 預設無 icon
                btn.installEventFilter(self)  # 綁定事件過濾器

        # 設定滑鼠移入的 icon
        self.hover_icon = QIcon(":/other/mode_dot_1.png")

    def show_Help_window(self):

        original_stderr = sys.stderr
        sys.stderr = open(os.devnull, 'w')

        from utils.webEngineUtil import SilentWebEnginePage
        """顯示 Help 視窗，確保只建立一次"""
        if self.Help_window is None:
            self.Help_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_Help_window = Ui_Help_Window()
            self.ui_Help_window.setupUi(self.Help_window)
            self.ui_Help_window.close_button.clicked.connect(partial(lambda:self.close_window("Help_window")))

            # 設定為彈出視窗，點擊外部時自動關閉
            self.Help_window.setWindowFlags(Qt.Popup)

            # 設定 PDF 檔案路徑 並載入 - 只在第一次創建時執行
            search_dir = os.path.abspath(os.getcwd())
            target_file = "MachRadarPro_Help_converted.pdf"
            for root, dirs, files in os.walk(search_dir):
                if target_file in files:
                    pdf_path = os.path.join(root, target_file)
                    break
            else:
                raise FileNotFoundError(f"PDF 檔案 {target_file} 未找到")

            logger.info(f"PDF 檔案路徑: {pdf_path}")
            url = QUrl.fromLocalFile(pdf_path)
            self.pdf_viewer = QWebEngineView()
            self.pdf_viewer.setPage(SilentWebEnginePage(self.pdf_viewer))

            self.Help_window.installEventFilter(self)
            
            # 配置 QWebEngineView 設定以減少錯誤
            settings = self.pdf_viewer.settings()
            settings.setAttribute(QWebEngineSettings.PluginsEnabled, True)
            settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, False)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, False)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)
            
            self.pdf_viewer.load(url)
            self.ui_Help_window.pdf_layout.addWidget(self.pdf_viewer)
        # 更新 Help_window 的大小以確保可以正確獲取建議尺寸
        self.Help_window.adjustSize()  # 確保視窗尺寸正確

        apply_scaling_to_window(self.Help_window, 600, 800, parent=self)

        sys.stderr = original_stderr

    def show_RecordScript_window(self, tool_list):
        """顯示 RecordScript 視窗，確保只建立一次"""
        if self.RecordScript_window is None:
            # create RecordScript_window
            self.RecordScript_window = RecordScriptWindow_implementation(self, tool_list)
            
            # connect signals
            self.RecordScript_window.sig_save_start_script.connect(self.sigSaveStartScript)
            self.RecordScript_window.sig_close_window.connect(partial(lambda: self.close_window("RecordScript_window")))
            self.RecordScript_window.sig_show_remind_window.connect(self.show_Remind_window)

            # set popup flags
            self.RecordScript_window.setWindowFlags(Qt.Popup)

            # install event filter
            self.RecordScript_window.installEventFilter(self)

        apply_scaling_to_window(self.RecordScript_window, 578, 612, parent=self)

    def enable_auto_range(self):
        """enable auto range for pygraph widgets (currently used by record script)"""
        if hasattr(self, "opengl_widget_bending"):
            self.opengl_widget_bending.plot_widget.enableAutoRange()
            logger.info("enabled auto range on bending")
        if hasattr(self, "opengl_widget_tension"):
            self.opengl_widget_tension.plot_widget.enableAutoRange()
            logger.info("enabled auto range on tension")
        if hasattr(self, "opengl_widget_torque"):
            self.opengl_widget_torque.plot_widget.enableAutoRange()
            logger.info("enabled auto range on torque")
        if hasattr(self, "opengl_widget_temp"):
            self.opengl_widget_temp.plot_widget.enableAutoRange()
            logger.info("enabled auto range on temp")

    def show_CO2e_window(self,co2data):
        """顯示 CO2e 視窗，確保只建立一次"""
        if self.CO2e_window is None:
            self.CO2e_window = QWidget(self)  # 使用 QWidget 作為獨立視窗
            self.ui_CO2e_window = Ui_CO2e_Window()
            self.ui_CO2e_window.setupUi(self.CO2e_window)

            # 綁定關閉按鈕的點擊事件
            self.ui_CO2e_window.CO2_close_btn.clicked.connect(partial(lambda:self.close_window("CO2e_window")))
            self.ui_CO2e_window.CO2_auto_btn.clicked.connect(lambda :self.co2_auto_up())

            # 填充 ComboBox 選項
            self.populate_combobox(self.ui_CO2e_window.CO2_K_comboBox, CO2_K_VALUES)
            self.populate_combobox(self.ui_CO2e_window.CO2_EF_comboBox, CO2_EF_VALUES)

            # 綁定 ComboBox 變更事件
            self.ui_CO2e_window.CO2_K_comboBox.currentIndexChanged.connect(lambda index: self.toggle_editable(self.ui_CO2e_window.CO2_K_comboBox, index))
            self.ui_CO2e_window.CO2_EF_comboBox.currentIndexChanged.connect(lambda index: self.toggle_editable(self.ui_CO2e_window.CO2_EF_comboBox, index))

            # 為 CO2_Pb_label 和 CO2_Ef_label 添加 Tooltip
            co2_pb_label = self.findChild(QLabel, "CO2_Pb_label")
            if co2_pb_label:
                self.tooltips_labels[co2_pb_label] = self.label_tooltips["CO2_Pb_label"]
                co2_pb_label.installEventFilter(self)  # 安裝事件過濾器

            co2_ef_label = self.findChild(QLabel, "CO2_EF_label")
            if co2_ef_label:
                self.tooltips_labels[co2_ef_label] = self.label_tooltips["CO2_EF_label"]
                co2_ef_label.installEventFilter(self)  # 安裝事件過濾器

            # 取得第一筆數據（因為 co2data 是 list of tuples）
            if co2data:
                co2_db_data = dict(co2data)
                # 只更新 `self.CO2_data` 裡已經存在的 key，避免錯位
                for key in self.CO2_data.keys():
                    if key in co2_db_data:
                        self.CO2_data[key] = co2_db_data[key]
            else:
                logger.info("CO2 資料表沒有數據，使用config.py中的預設值")
                self.CO2_data = dict(DEFAULT_CO2_DATA)
            self.CO2e_comparer = DataComparer(self.CO2_data)
            self.CO2e_comparer_temp = self.CO2e_comparer.begin_edit()

            # 定義對應的鍵與 UI 元件
            # 初始化 ComboBox 值（基於當前 CO2_data 的值）
            self.initialize_combobox(self.ui_CO2e_window.CO2_K_comboBox, CO2_K_VALUES, "MS_CO2_K")
            self.initialize_combobox(self.ui_CO2e_window.CO2_EF_comboBox, CO2_EF_VALUES, "MS_CO2_EF")


            # **初始化所有輸入框**
            for key, widget in self.get_CO2e_fields().items():
                value = self.CO2_data.get(key, "")
                if value == "" or value is None:
                    formatted_value = ""
                else:
                    formatted_value = f"{value:.{CO2_SETTING_DISPLAY_DECIMALS}f}" \
                        if isinstance(value, float) else str(value)
                widget.setText(formatted_value)
                InputValidator.apply_validator(widget, "float")

            # 綁定儲存按鈕
            self.ui_CO2e_window.CO2_save_btn.clicked.connect(self.co2eSaveBtn)

            # 設定為彈出視窗，點擊外部時自動關閉
            self.CO2e_window.setWindowFlags(Qt.Popup)
            # 安裝事件過濾器，監聽關閉事件
            self.CO2e_window.installEventFilter(self)

        apply_scaling_to_window(self.CO2e_window, 450, 600, parent=self)

    def show_ScriptStatus_window(self):
        """顯示 ScriptStatus 視窗，確保只建立一次"""
        if self.ScriptStatus_window is None:
            self.ScriptStatus_window = DraggableDialog(self) 
            self.ui_ScriptStatus_window = Ui_ScriptStatus_window()
            self.ui_ScriptStatus_window.setupUi(self.ScriptStatus_window)

            # 綁定按鈕的點擊事件
            self.ui_ScriptStatus_window.expand_btn.clicked.connect(self.expand_collapse_status)
            self.ui_ScriptStatus_window.cancel_btn.clicked.connect(partial(lambda: self.sigScriptAction.emit("cancel")))

            # 隱藏展開區域
            self.ui_ScriptStatus_window.status_group.hide()

            # 設定為非模態對話框，但無法點擊外部關閉
            self.ScriptStatus_window.setModal(False)  # 設定為非模態，不阻擋主視窗

            # 更新大小
            self.ScriptStatus_window.resize(self.ScriptStatus_window.sizeHint())

        # 讓 ScriptStatus_window 顯示在 MainWindow 正中央
        global_co2_position = self.Label_ECO.mapToGlobal(QPoint(0, 0))
        logger.warning(f"global_co2_position: x={global_co2_position.x()}, y={global_co2_position.y()}")
        script_status_window_width = self.ScriptStatus_window.size().width()
        center_x = global_co2_position.x() - script_status_window_width - 20
        center_y = global_co2_position.y()
        logger.warning(f"ScriptStatus_window geometry: x={center_x}, y={center_y}")
        logger.warning(f"ScriptStatus_window width: {script_status_window_width}")
        self.ScriptStatus_window.move(center_x, center_y)

        self.ScriptStatus_window.show()
        # self.ScriptStatus_window.raise_()  # 確保視窗浮在最前面

    def expand_collapse_status(self):
        if self.ui_ScriptStatus_window.expand_btn.isChecked(): # expand
            self.ui_ScriptStatus_window.expand_btn.setText("-")
            self.ui_ScriptStatus_window.status_group.show()
            self.ScriptStatus_window.resize(self.ScriptStatus_window.sizeHint())
        else: # collapse
            self.ui_ScriptStatus_window.expand_btn.setText("+")
            self.ui_ScriptStatus_window.status_group.hide()
            self.ScriptStatus_window.resize(self.ScriptStatus_window.sizeHint())

    def on_script_pause_play(self):
        """處理暫停/播放按鈕點擊"""
        if self.ui_ScriptStatus_window.pause_play_btn.isChecked():
            self.sigScriptAction.emit("pause")
            self.ui_ScriptStatus_window.pause_play_btn.setText("繼續")
        else:
            self.sigScriptAction.emit("resume")
            self.ui_ScriptStatus_window.pause_play_btn.setText("暫停")

    def update_textbrowser(self, text):
        """更新 textbrowser 文字和高度"""
        # update text
        self.ui_ScriptStatus_window.status_text.setPlainText(text)

        # calculate new height
        textbrowser_height = self.ui_ScriptStatus_window.status_text.document().size().height() * (self.ui_ScriptStatus_window.status_text.fontMetrics().lineSpacing() + 3) + 6
        self.ui_ScriptStatus_window.status_text.setFixedHeight(textbrowser_height)
        
        # apply new height
        self.ui_ScriptStatus_window.status_group.resize(self.ui_ScriptStatus_window.status_group.sizeHint())
        self.ScriptStatus_window.resize(self.ScriptStatus_window.sizeHint())

    def update_script_status(self, status_text):
        """更新腳本狀態顯示"""
        if hasattr(self, 'ui_ScriptStatus_window') and self.ui_ScriptStatus_window is not None:
            self.update_textbrowser(status_text)

    def show_script_finished(self):
        """顯示腳本執行完畢，等用戶關閉視窗"""
        if hasattr(self, 'ui_ScriptStatus_window') and self.ui_ScriptStatus_window is not None:
            # update texts
            self.ui_ScriptStatus_window.title_label.setText("腳本執行完畢！")
            self.ui_ScriptStatus_window.status_group.hide()

            # cleap up connections
            self.ui_ScriptStatus_window.cancel_btn.clicked.disconnect()
            self.ui_ScriptStatus_window.expand_btn.clicked.disconnect()

            # hide status and buttons
            self.ui_ScriptStatus_window.expand_btn.setChecked(False)
            self.expand_collapse_status()
            self.ui_ScriptStatus_window.cancel_btn.hide()
            self.ui_ScriptStatus_window.expand_btn.hide()
            
            # set timer to close
            QTimer.singleShot(5000, partial(lambda: self.close_window("ScriptStatus_window")))

    def show_script_error(self, error_message, user_action_required=False):
        """顯示腳本錯誤，並根據 user_action 顯示按鈕組"""
        if hasattr(self, 'ui_ScriptStatus_window') and self.ui_ScriptStatus_window is not None:
            self.update_textbrowser(f"錯誤: {error_message}")

    def set_Main_Window(self, data):

        try:
            current_data = data
            # logger.debug(type(current_data))  # 先檢查資料型態
            # logger.debug(current_data)  # 輸出資料確認內容
            toolName = current_data["toolname"]
            self.Btn_Toolname.setText(
                QCoreApplication.translate("main_window", toolName, None)
            )
            # self.Btn_Toolname.setText(toolName)
        except:
            pass

    def eventFilter(self, obj, event):
        """統一處理按鈕 hover 事件 & 視窗關閉事件"""

        # 1️⃣ 按鈕 Hover 事件處理 (for ModeSwitch_window)
        if hasattr(self, "modeswitch_buttons") and obj in self.modeswitch_buttons:
            icon_btn = self.modeswitch_buttons[obj]
            if event.type() == QEvent.Enter:
                icon_btn.setIcon(self.hover_icon)  # 滑鼠移入時顯示 icon
            elif event.type() == QEvent.Leave:
                icon_btn.setIcon(QIcon())  # 滑鼠離開時移除 icon

        # 2️⃣ Tooltip 處理
        if obj in self.tooltips_buttons:
            if event.type() == QEvent.Enter:
                self.tooltip_label.show_tooltip(self.tooltips_buttons[obj], obj.mapToGlobal(obj.rect().bottomLeft()))
            elif event.type() == QEvent.Leave:
                self.tooltip_label.hide()

        if obj in self.tooltips_labels:
            if event.type() == QEvent.Enter:
                self.tooltip_label.show_tooltip(self.tooltips_labels[obj], obj.mapToGlobal(obj.rect().bottomLeft()))
            elif event.type() == QEvent.Leave:
                self.tooltip_label.hide()

        if obj in self.tooltips_buttons_up:
            if event.type() == QEvent.Enter:
                self.tooltip_label_up.show_tooltip(self.tooltips_buttons_up[obj], obj.mapToGlobal(obj.rect().topLeft()))
            elif event.type() == QEvent.Leave:
                self.tooltip_label_up.hide()

        # 3️⃣ Tab 鍵事件
        # 當按鈕獲得或失去焦點時更新樣式
        if event.type() == event.FocusIn and obj in self.tab_buttons:
            self.tab_current_index = self.tab_buttons.index(obj)
            self.update_button_styles()
            return False
        # 處理 Tab 鍵
        if event.type() == event.KeyPress and event.key() == Qt.Key_Tab:
            self.switch_focus()
            return True


        # 4️⃣ 視窗 Close 事件處理 (for Setting_window, Filter_window, etc.)
        window_list = [
            "Setting_window",
            "Filter_window",
            "View_window",
            "Event_window",
            "ModeSwitch_window",
            "RecordScript_window",
            "Help_window",
            "CO2e_window",
            "Toolinfo_window",
            "Toollist_window",
            "RecordSetting_window",
            "ScriptStatus_window",
        ]
        for window_name in window_list:
            if hasattr(self, window_name) and obj == getattr(self, window_name):
                if event.type() == QEvent.Close:
                    window = getattr(self, window_name)
                    window.close()  # 關閉窗口
                    window.deleteLater()
                    setattr(
                        self, window_name, None
                    )
                    # logger.info("視窗關閉None")
                    logger.info(f"視窗關閉: {window_name}")
                        
                    

        return super().eventFilter(obj, event)  # 繼續處理其他事件

    def eventLinkBtn(self, data):
        # TAG: eventLinkBtn
        # 連線設定IP
        logger.info(data)
        logger.debug("Pressed Link")
        # logger.info(f"{data['toolmac']}")
        self.connect_mac = data["toolmac"]
        
        # 重置所有標籤顏色為白色
        for count, tool in enumerate(self.tool_list):
            tool_name_label = getattr(self.ui_Toollist_window, f"toolname_label_{count+1}", None)
            if tool_name_label:
                tool_name_label.setStyleSheet("color: rgb(255, 255, 255);")
        
        # 將當前連接的標籤設為綠色
        for count, tool in enumerate(self.tool_list):
            if tool["toolmac"] == self.connect_mac:
                tool_name_label = getattr(self.ui_Toollist_window, f"toolname_label_{count+1}", None)
                if tool_name_label:
                    tool_name_label.setStyleSheet("color:#7AFEC6;")
        
        self.view_current_data.emit(data)
        # 刷新視窗
        self.Toollist_window.repaint()
        # IP 資料回傳
        # pass

    def eventTareBtn(self):
        """處理Tare按鈕點擊事件"""
        # TODO: 顯示 TARE 狀態 直到完成 並關閉
        logger.info("Tare Tare Tare")
        logger.info("Show Tare windows")

        if self.Btn_Link.isChecked():
            return

        # 顯示初始提示視窗
        self.show_pop_up_window("校正中，請稍後")

        # 設置動態點點
        self.original_message = "校正中，請稍後"
        self.dot_count = 0  # 初始化點點數量
        self.max_dots = 3   # 最多顯示3顆點點
        self.dot_timer = QTimer()

        # 定義動態點點更新函數
        def update_dots():
            if hasattr(self, 'pop_up_dialog') and self.pop_up_dialog is not None:
                self.dot_count = (self.dot_count % self.max_dots) + 1  # 循環增加點點
                self.pop_up_dialog.update_label(f"{self.original_message}{'.' * self.dot_count}")

        # 連接計時器和更新函數
        self.dot_timer.timeout.connect(update_dots)
        self.dot_timer.start(250)  # 每500毫秒更新一次

        # 模擬操作完成後關閉視窗
        QTimer.singleShot(10000, self.close_hint_window)  # 10秒後關閉

        logger.debug("eventTareBtn end")

    # 濾波 存檔事件
    def filterSaveBtn(self):
        """處理Filter儲存按鈕點擊事件"""
        logger.info("Filter Save Button Clicked")

        self.filter_comparer_temp["Lowfilter_edit"] = int(self.ui_Filter_window.Lowfilter_lineEdit.text())
        self.filter_comparer_temp["Highfilter_edit"] = int(self.ui_Filter_window.Highfilter_lineEdit.text())
        self.filter_comparer_temp["SGfilter_edit"] = int(self.ui_Filter_window.SGfilter_lineEdit.text())
        self.filter_comparer_temp["MAfilter_edit"] = int(self.ui_Filter_window.MAfilter_lineEdit.text())

        # 檢查是否有差異
        self.filter_comparer.update_temp(self.filter_comparer_temp)
        if self.filter_comparer.has_differences():
            logger.info("發現資料差異")
            # TODO: 修正神奇數字269?
            result = self.show_Confirm_Window("是否儲存已更改的濾波器269？")
            if result == QDialog.Accepted:
                logger.info("使用者選擇了 是")
                self.filter_comparer.commit()
                self.filter_data = self.filter_comparer.get_original()

            else:
                logger.info("使用者選擇了 否")
                self.filter_comparer.discard()
        else:    
            logger.info("沒有差異，不需要儲存")
            self.filter_comparer.discard()
            self.close_window("Filter_window")
        # self.update_record_state.emit(self.filter_data)

    # TODO: 警示/警告 存檔事件 20250715
    def viewSetWarningSaveBtn(self):
        """處理 警示/警告 儲存按鈕點擊事件"""
        logger.info("警示/警告 Save Button Clicked")

        # 🔥 儲存前先驗證所有警示/警告欄位
        is_valid, error_message = InputValidator.validate_view_warning_fields(self.ui_View_window)
        if not is_valid:
            # 驗證失敗，顯示錯誤訊息
            logger.error(f"警示/警告欄位驗證失敗: {error_message}")
            self.show_Remind_window(error_message)
            return  # 停止執行儲存
        
        # 驗證成功，繼續原有的儲存邏輯
        logger.info("警示/警告欄位驗證成功，開始儲存")

        # 更新 CF_Auto_value
        self.view_setting_comparer_temp["CF_Auto_value"].update({
            "maximum": float(self.ui_View_window.CF_max_lineedit.text() or 0.0),
            "minimum": float(self.ui_View_window.CF_min_lineedit.text() or 0.0),
            "warning": float(self.ui_View_window.CF_warning_lineedit.text() or 0.0),
            "alarm": float(self.ui_View_window.CF_alarm_lineedit.text() or 0.0)
        })

        # 更新 FxFy_Auto_value
        self.view_setting_comparer_temp["FxFy_Auto_value"].update({
            "peak_plus": float(self.ui_View_window.FxFy_pos_peak_lineedit.text() or 0.0),
            "peak_minus": float(self.ui_View_window.FxFy_neg_peak_lineedit.text() or 0.0)
        })

        # 更新 Fz_Auto_value
        self.view_setting_comparer_temp["Fz_Auto_value"].update({
            "peak_plus": float(self.ui_View_window.Fz_pos_peak_lineedit.text() or 0.0),
            "peak_minus": float(self.ui_View_window.Fz_neg_peak_lineedit.text() or 0.0),
            "alarm_plus": float(self.ui_View_window.Fz_pos_alarm_lineedit.text() or 0.0),
            "alarm_minus": float(self.ui_View_window.Fz_neg_alarm_lineedit.text() or 0.0),
            "warning_plus": float(self.ui_View_window.Fz_pos_warning_lineedit.text() or 0.0),
            "warning_minus": float(self.ui_View_window.Fz_neg_warning_lineedit.text() or 0.0)
        })

        # 更新 Torque_Auto_value
        self.view_setting_comparer_temp["Torque_Auto_value"].update({
            "peak_plus": float(self.ui_View_window.Torque_pos_peak_lineedit.text() or 0.0),
            "peak_minus": float(self.ui_View_window.Torque_neg_peak_lineedit.text() or 0.0),
            "alarm_plus": float(self.ui_View_window.Torque_pos_alarm_lineedit.text() or 0.0),
            "alarm_minus": float(self.ui_View_window.Torque_neg_alarm_lineedit.text() or 0.0),
            "warning_plus": float(self.ui_View_window.Torque_pos_warning_lineedit.text() or 0.0),
            "warning_minus": float(self.ui_View_window.Torque_neg_warning_lineedit.text() or 0.0)
        })

        # 更新 Temp_Auto_value
        self.view_setting_comparer_temp["Temp_Auto_value"].update({
            "maximum": float(self.ui_View_window.Temp_max_lineedit.text() or 0.0),
            "minimum": float(self.ui_View_window.Temp_min_lineedit.text() or 0.0),
            "warning": float(self.ui_View_window.Temp_warning_lineedit.text() or 0.0),
            "alarm": float(self.ui_View_window.Temp_alarm_lineedit.text() or 0.0)
        })

        # 更新其他設定值
        self.view_setting_comparer_temp.update({
            "view_OpenAW_value": {"state": self.ui_View_window.view_OpenAW_checkBox.isChecked()},
            "view_HideRing_value": {"state": self.ui_View_window.view_HideRing_checkBox.isChecked()},
            "view2_HideScale_value": {"state": self.ui_View_window.view2_HideScale_checkBox.isChecked()},
            "color_spectrum": self.view_setting_comparer_temp.get("color_spectrum", 0)  # 保留已設定的 color_spectrum 值
        })

        # 檢查是否有差異
        self.view_setting_comparer.update_temp(self.view_setting_comparer_temp)
        if self.view_setting_comparer.has_differences():
            logger.info("發現資料差異")
            result = self.show_Confirm_Window("是否儲存已更改的設定？")
            if result == QDialog.Accepted:
                logger.info("使用者選擇了 是")
                self.view_setting_comparer.commit()
                self.view_setting_data = self.view_setting_comparer.get_original()
                # 更新 colorbar 和 3D 尺度
                self.opengl_colorbar.color_spectrum = self.view_setting_data["color_spectrum"]
                self.opengl_colorbar.CFmax = self.view_setting_data["CF_Auto_value"]["maximum"]
                self.opengl_colorbar.setupColorBar() #更新 color bar 顏色

                # 更新 3D 視圖的顏色設置
                self.opengl_widget_instance_1.update_color_settings(self.view_setting_data)

                self.opengl_widget_instance_1.update_scene(
                    axis_length=self.view_setting_data["CF_Auto_value"]["maximum"],
                    grid_size=self.view_setting_data["CF_Auto_value"]["maximum"]*2,
                    grid_spacing=self.view_setting_data["CF_Auto_value"]["maximum"]/10,
                    tick_spacing=self.view_setting_data["CF_Auto_value"]["maximum"]/10*2
                    )
                self.opengl_widget_instance_1.update_CFdata(self.view_setting_data["CF_Auto_value"]["maximum"])
                distance = self.view_setting_data["CF_Auto_value"]["maximum"] * 150  # 將軸長度乘以一個係數來設定相機距離
                self.opengl_widget_instance_1.view.setCameraPosition(distance=distance)

                # 更新 2D Y 刻度
                if self.view_setting_data["Fz_Auto_value"]['state']:
                    self.opengl_widget_tension.set_auto_range() # 設定Y軸範圍自動   
                else:
                    self.opengl_widget_tension.set_y_range(self.view_setting_data["Fz_Auto_value"]['peak_minus'],self.view_setting_data["Fz_Auto_value"]['peak_plus']) # 設定Y軸範圍
        
                # self.opengl_widget_torque
                if self.view_setting_data["Torque_Auto_value"]['state']:
                    self.opengl_widget_torque.set_auto_range() # 設定Y軸範圍自動
                else:
                    self.opengl_widget_torque.set_y_range(self.view_setting_data["Torque_Auto_value"]['peak_minus'],self.view_setting_data["Torque_Auto_value"]['peak_plus']) # 設定Y軸範圍

                # opengl_widget_temp
                if self.view_setting_data["Temp_Auto_value"]['state']:
                    self.opengl_widget_temp.set_auto_range() # 設定Y軸範圍自動
                else:
                    self.opengl_widget_temp.set_y_range(self.view_setting_data["Temp_Auto_value"]['minimum'],self.view_setting_data["Temp_Auto_value"]['maximum']) # 設定Y軸範圍

                # self.opengl_widget_bending
                if self.view_setting_data["FxFy_Auto_value"]['state']:
                    self.opengl_widget_bending.set_auto_range() # 設定Y軸範圍自動
                else:
                    self.opengl_widget_bending.set_y_range(self.view_setting_data["FxFy_Auto_value"]['peak_minus'],self.view_setting_data["FxFy_Auto_value"]['peak_plus']) # 設定Y軸範圍

            else:
                logger.info("使用者選擇了 否")
                self.view_setting_comparer.discard()
        else:    
            logger.info("沒有差異，不需要儲存")
            self.view_setting_comparer.discard()
            self.close_window("View_window")

        # TODO : 刷新警示警告背景狀態 #檢查警示/警告 事件 
        # 判斷讀檔更新
        if self.view_read_data:
            logger.error("有讀檔案")
            self.view_read_data = False
             # 更新 colorbar 和 3D 尺度
            self.opengl_colorbar.color_spectrum = self.view_setting_data["color_spectrum"]
            self.opengl_colorbar.CFmax = self.view_setting_data["CF_Auto_value"]["maximum"]
            self.opengl_colorbar.setupColorBar() #更新 color bar 顏色

            # 更新 3D 視圖的顏色設置
            self.opengl_widget_instance_1.update_color_settings(self.view_setting_data)

            self.opengl_widget_instance_1.update_scene(
                    axis_length=self.view_setting_data["CF_Auto_value"]["maximum"],
                    grid_size=self.view_setting_data["CF_Auto_value"]["maximum"]*2,
                    grid_spacing=self.view_setting_data["CF_Auto_value"]["maximum"]/10,
                    tick_spacing=self.view_setting_data["CF_Auto_value"]["maximum"]/10*2
                    )
            self.opengl_widget_instance_1.update_CFdata(self.view_setting_data["CF_Auto_value"]["maximum"])
            distance = self.view_setting_data["CF_Auto_value"]["maximum"] * 150  # 將軸長度乘以一個係數來設定相機距離
            self.opengl_widget_instance_1.view.setCameraPosition(distance=distance)

            # 更新 2D Y 刻度
            if self.view_setting_data["Fz_Auto_value"]['state']:
                self.opengl_widget_tension.set_auto_range() # 設定Y軸範圍自動   
            else:
                self.opengl_widget_tension.set_y_range(self.view_setting_data["Fz_Auto_value"]['peak_minus'],self.view_setting_data["Fz_Auto_value"]['peak_plus']) # 設定Y軸範圍
        
            # self.opengl_widget_torque
            if self.view_setting_data["Torque_Auto_value"]['state']:
                self.opengl_widget_torque.set_auto_range() # 設定Y軸範圍自動
            else:
                self.opengl_widget_torque.set_y_range(self.view_setting_data["Torque_Auto_value"]['peak_minus'],self.view_setting_data["Torque_Auto_value"]['peak_plus']) # 設定Y軸範圍

            # opengl_widget_temp
            if self.view_setting_data["Temp_Auto_value"]['state']:
                self.opengl_widget_temp.set_auto_range() # 設定Y軸範圍自動
            else:
                self.opengl_widget_temp.set_y_range(self.view_setting_data["Temp_Auto_value"]['minimum'],self.view_setting_data["Temp_Auto_value"]['maximum']) # 設定Y軸範圍

            # self.opengl_widget_bending
            if self.view_setting_data["FxFy_Auto_value"]['state']:
                self.opengl_widget_bending.set_auto_range() # 設定Y軸範圍自動
            else:
                self.opengl_widget_bending.set_y_range(self.view_setting_data["FxFy_Auto_value"]['peak_minus'],self.view_setting_data["FxFy_Auto_value"]['peak_plus']) # 設定Y軸範圍
        
        
    
    def co2eSaveBtn(self):
        """處理 CO2e 儲存按鈕點擊事件"""
        logger.info("co2e Save Button Clicked")
        
        # 更新 CO2_K 和 CO2_EF 的值
        k_selected_text = self.ui_CO2e_window.CO2_K_comboBox.currentText()
        if k_selected_text in CO2_K_VALUES and CO2_K_VALUES[k_selected_text] is not None:
            self.CO2e_comparer_temp["MS_CO2_K"] = CO2_K_VALUES[k_selected_text]
        elif self.ui_CO2e_window.CO2_K_comboBox.isEditable():
            try:
                self.CO2e_comparer_temp["MS_CO2_K"] = float(k_selected_text)
            except ValueError:
                pass
        
        ef_selected_text = self.ui_CO2e_window.CO2_EF_comboBox.currentText()
        if ef_selected_text in CO2_EF_VALUES and CO2_EF_VALUES[ef_selected_text] is not None:
            self.CO2e_comparer_temp["MS_CO2_EF"] = CO2_EF_VALUES[ef_selected_text]
        elif self.ui_CO2e_window.CO2_EF_comboBox.isEditable():
            try:
                self.CO2e_comparer_temp["MS_CO2_EF"] = float(ef_selected_text)
            except ValueError:
                pass

        # 儲存其他數據
        for key, widget in self.get_CO2e_fields().items():
            try:
                self.CO2e_comparer_temp[key] = float(widget.text())
            except ValueError:
                self.CO2e_comparer_temp[key] = widget.text()  # 儲存為文字

        if self.CO2e_comparer_temp["init_status"] == 0:
            self.CO2e_comparer_temp["init_status"] = 1  # 標記為已初始化

        # 檢查差異並處理
        self.CO2e_comparer.update_temp(self.CO2e_comparer_temp)
        if self.CO2e_comparer.has_differences():
            logger.info("發現資料差異")
            result = self.show_Confirm_Window("是否儲存已更改的 CO2e？")
            if result == QDialog.Accepted:
                logger.info("使用者選擇了 是")
                self.CO2e_comparer.commit()  # 儲存更改
                self.CO2_data = self.CO2e_comparer.get_original()  # 更新原始資料
                self.update_co2_data.emit(self.CO2_data) #將資料打回controller
            else:
                logger.info("使用者選擇了 否")
                self.CO2e_comparer.discard()  # 放棄更改
        else:
            logger.info("沒有變更，不需要儲存")
            self.CO2e_comparer.discard()
            self.close_window("CO2e_window")

    def co2_auto_up(self):
        """處理 CO2e 自動更新按鈕點擊事件"""
        logger.info("CO2e Auto Update Button Clicked")

        # 更新 temp 數據
        self.CO2e_comparer_temp = dict(DEFAULT_CO2_DATA)

        # 更新 UI 顯示
        for key, widget in self.get_CO2e_fields().items():
            if key in self.CO2e_comparer_temp:
                value = self.CO2e_comparer_temp[key]
                formatted_value = f"{value:.{CO2_SETTING_DISPLAY_DECIMALS}f}" \
                    if isinstance(value, float) else str(value)
                widget.setText(formatted_value)

        # 更新 ComboBox
        self.initialize_combobox(self.ui_CO2e_window.CO2_K_comboBox, CO2_K_VALUES, "MS_CO2_K")
        self.initialize_combobox(self.ui_CO2e_window.CO2_EF_comboBox, CO2_EF_VALUES, "MS_CO2_EF")

        logger.info(f"自動更新後的數據: {self.CO2e_comparer_temp}")

    def update_holder_data(self, data):
        # logger.error(f"XX收到XX: {data}")
        if not data:
            return
        self.count += 1
        """ 更新 UI 文字框 """
        (
            MS_BendingX,
            MS_BendingY,
            MS_BendingXY,
            MS_Tension,
            MS_Torsion,
            MS_Temperature,
            MS_ADXL_X,
            MS_ADXL_Y,
            MS_ADXL_Z,
            temp_RSSI,
            Charging_Flag_temp ,
            sleep_mode_temp, 
            temp_battery,
            co2_g
        ) = data
        # logger.error(f"收到: {MS_BendingX}")
        # logger.error(f"count: {self.count}")

        # self.opengl_widget_instance_1.update_points(data,self.plot_type) # 更新 OpenGL 畫面
        self.sigUpdateOpenglWidget.emit(data,self.plot_type)
        self.sigUpdateBending.emit(MS_BendingX, MS_BendingY, MS_BendingXY)
        self.sigUpdateTension.emit(MS_Tension)
        self.sigUpdateTorque.emit(MS_Torsion)
        self.sigUpdateTemp.emit(MS_Temperature)
        self.WIFI_calculation(temp_RSSI)
        # TAG: 需要一點延遲 暫定100ms，在測試調整
        self.Max_avg(MS_BendingXY,MS_Tension,MS_Torsion,MS_Temperature)
      
        self.Battery_calculation(Charging_Flag_temp, temp_battery)
        self.update_sleep_mode_button(sleep_mode_temp)
        self.UpdateCO2g(co2_g)

    #-- 電池計算function --
    def Battery_calculation(self, Charging_Flag_temp, temp_battery):
        '''
        電壓範圍2.84~4v	=>電壓差4v-2.84v=1.16v
    
        4v降至3.7v=>約3小時
        3.7v降至3.57v=>約3小時
        3.57v降至3.37v=>約3小時
        3.37v降至3.0v=>約1小時
        3.0v降至2.84v=>約剩5分鐘 閃爍

        3.7v~4v	    滿格
        3.57v~3.7v	   3格
        3.37v~3.57v   2格
        3v~3.37v	   1格
        3v以下	      閃爍
        '''
        Charging_Flag = Charging_Flag_temp
        Battery_V = temp_battery
        temp_value = 0

        if Battery_V>=4:  
            temp_value=100
        #大概3小時，100%~25%
        elif 3.5<=Battery_V and Battery_V<4:          
            temp_value = round(75-(75/(0.5/(4-Battery_V)))+25)
        #大概1小時，25%~5%
        elif 3.3<=Battery_V and Battery_V<3.5:  
            temp_value = round(20-(20/(0.2/(3.5-Battery_V)))+5)
        #大概8分鐘，5%~0%，0%閃圖案
        elif 3.0<Battery_V and Battery_V<3.3:  
            temp_value = round(5-(5/(0.3/(3.3-Battery_V))))
        elif  Battery_V<=3.0: 
            temp_value=0

        if self.Battery_percent > temp_value:  #解決降太快的問題
            self.Battery_percent-=1  
        else:
            self.Battery_percent = temp_value
            
        if self.Battery_percent == -1:  #解決沒電顯示不會出現-1%
            self.Battery_percent = 0

        if not Charging_Flag:

            #100%，4/4圖案
            if self.Battery_percent>=100:  
                self.Battery.setPixmap(QPixmap(u":/battery/battery_4.png"))
        
            #大概3小時，100%~25%，4/4~2/4圖案
            elif 100>self.Battery_percent and self.Battery_percent>=25:          
                if self.Battery_percent>=75:
                    self.Battery.setPixmap(QPixmap(u":/battery/battery_4.png"))
                elif 75>self.Battery_percent and self.Battery_percent>=50:
                    self.Battery.setPixmap(QPixmap(u":/battery/battery_3.png"))
                else:
                    self.Battery.setPixmap(QPixmap(u":/battery/battery_2.png"))
                
            #大概1小時，25%~5%，1/4圖案
            elif 25>self.Battery_percent and self.Battery_percent>=5:  
                self.Battery.setPixmap(QPixmap(u":/battery/battery_1.png"))
            
            #大概8分鐘，5%~0%，0%閃圖案
            else:  
                self.Battery.setPixmap(QPixmap(u":/battery/battery_0.png"))
                
        else:
            self.Battery.setPixmap(QPixmap(u":/battery/battery_5.png"))
    
    def update_sleep_mode_button(self,sleep_mode_temp):
        """ 根據 sleep_mode_temp 狀態更新按鈕圖片 """
        # logger.debug(f"sleep_mode_temp: {sleep_mode_temp}")
        if not sleep_mode_temp :  # 睡眠未開啟
            self.Btn7_sleep_mode.setIcon(QIcon(u":/bnt_vp_sleep_mode/btn_vp_sleep_off.png"))  # 設定為關閉狀態的圖片
        else:  # 睡眠已開啟
            self.Btn7_sleep_mode.setIcon(QIcon(u":/bnt_vp_sleep_mode/btn_vp_sleep_on.png"))  # 設定為開啟狀態的圖片
        
    def show_Confirm_Window(self, message):
        """顯示 Confirm 視窗"""
        Confirm_dialog = Ui_Confirm_Window(message)

        # 讓 dialog 顯示在 MainWindow 正中央
        main_rect = self.geometry()
        Confirm_dialog.adjustSize()  # 確保視窗尺寸正確
        dialog_size = Confirm_dialog.size()
        
        center_x = main_rect.x() + (main_rect.width() - dialog_size.width()) // 2
        center_y = main_rect.y() + (main_rect.height() - dialog_size.height()) // 2
        Confirm_dialog.move(center_x, center_y)

        result = Confirm_dialog.exec_()
        if result == QDialog.Accepted:
            logger.info("使用者選擇了 是")
        else:
            logger.info("使用者選擇了 否")

        return result  # **新增這行，讓方法回傳結果**

    def show_Remind_window(self, message):
        """顯示 Remind 視窗"""
        self.Remind_dialog = Ui_Remind_Window(message)  # 用 self.Remind_dialog 保存，避免瞬間被回收

        # 讓 Remind_dialog 顯示在 MainWindow 正中央
        main_rect = self.geometry()
        settings_size = self.Remind_dialog.size()  # 取得建議尺寸
        center_x = main_rect.x() + (main_rect.width() - settings_size.width()) // 2
        center_y = main_rect.y() + (main_rect.height() - settings_size.height()) // 2
        self.Remind_dialog.move(center_x, center_y)

        self.Remind_dialog.show()  # 使用 show() 讓主視窗不受影響

    def show_pop_up_window(self, message):
        """顯示 hint 視窗"""
        if hasattr(self, 'dot_timer') and self.dot_timer.isActive():
            self.dot_timer.stop()
        self.pop_up_dialog = Ui_pop_up_Window(message)

        # 讓 pop_up_dialog 顯示在 MainWindow 正中央
        main_rect = self.geometry()
        settings_size = self.pop_up_dialog.size()  # 取得建議尺寸
        center_x = main_rect.x() + (main_rect.width() - settings_size.width()) // 2
        center_y = main_rect.y() + (main_rect.height() - settings_size.height()) // 2
        self.pop_up_dialog.move(center_x, center_y)

        self.pop_up_dialog.show()  # 使用 show() 讓主視窗不受影響
        self.original_message = message
        # QTimer.singleShot(5000, self.close_hint_window)

    def close_hint_window(self):
        """關閉提示視窗"""
        if hasattr(self, 'dot_timer') and self.dot_timer.isActive():
            self.dot_timer.stop()
        if hasattr(self, 'pop_up_dialog'):
            self.pop_up_dialog.close()

    def show_message_box_Window(self, message):
        """顯示 Message Box 視窗"""
        self.message_box_dialog = Ui_message_box_Window(
            message
        )  # 用 self.dialog 保存，避免瞬間被回收

        # 讓 dialog 顯示在 MainWindow 正中央
        main_rect = self.geometry()
        settings_size = self.message_box_dialog.size()  # 取得建議尺寸
        center_x = main_rect.x() + (main_rect.width() - settings_size.width()) // 2
        center_y = main_rect.y() + (main_rect.height() - settings_size.height()) // 2
        self.message_box_dialog.move(center_x, center_y)

        self.message_box_dialog.show()  # 使用 show() 讓主視窗不受影響

    def close_window(self, window_name):
        """關閉指定名稱的窗口並將其設為 None"""
        if hasattr(self, window_name):  # 檢查 self 是否有該屬性
            window = getattr(self, window_name)  # 獲取該屬性的值
            if window is not None:
                window.close()  # 關閉窗口
                window.deleteLater()
                setattr(self, window_name, None)  # 設置為 None
                logger.info(f"close_window {window_name} None")

    def closeEvent(self, event):
        """關閉視窗時的處理"""
        logger.info("發送 closeEvent 信號")
        # TODO: Tare_staging 先確認暫存與刀庫資料是否不同
        self.sigCloseEvent.emit()

    #-- WIFI RSSI 計算function --
    def WIFI_calculation(self,wifi_rssi):
        self.wifi_rssi.setText("-"+str(wifi_rssi)+"dBm")

        #沒連接
        if wifi_rssi==0:
            self.WIFI.setPixmap(QPixmap(u":/wifi/wifi_0.png"))
        #1格訊號圖案(差)
        elif wifi_rssi>=70:  
            self.WIFI.setPixmap(QPixmap(u":/wifi/wifi_1.png"))
        #2格訊號圖案
        elif 70>wifi_rssi and wifi_rssi>=50:  
            self.WIFI.setPixmap(QPixmap(u":/wifi/wifi_2.png"))
        #3格訊號圖案
        elif 50>wifi_rssi and wifi_rssi>=30:  
           self.WIFI.setPixmap(QPixmap(u":/wifi/wifi_3.png"))
        #4格訊號圖案(滿)
        else:  
            self.WIFI.setPixmap(QPixmap(u":/wifi/wifi_4.png"))

        # self.wifi_rssi.setText("-"+str(temp_RSSI)+"dBm")
        # RSSI 計算公式
        # RSSI = -10n log(d) + A
        # RSSI: 接收訊號強度
        # n: 環境衰減因子
        # d: 距離
        # A: 發射端和接收端固有的訊號衰減因子
        # n = 2.7 ~ 4.3
        # A = 27 ~ 30
        # d = 10^((A - RSSI) / (10n))
        # logger.info(f"RSSI: {wifi_rssi}")
        # n = 3.0
        # A = 30
        # d = 10**((A - wifi_rssi) / (10 * n))
        # logger.info(f"距離: {d}")
        # return d

    # TAG: -- 最大、平均值計算function --
    def Max_avg(self, MS_BendingXY, MS_Tension, MS_Torsion, MS_Temperature):

        # CF 最大、平均值
        self.CF_max.setText("Max : "+str(np.round( np.max(MS_BendingXY),self.display_decimal_places)))
        self.CF_avg.setText("Avg : "+str(np.round( np.mean(MS_BendingXY), self.display_decimal_places)))

        # Fz 最大、平均值
        self.Fz_max.setText("Max : "+str(np.round( np.max(MS_Tension),self.display_decimal_places)))
        self.Fz_avg.setText("Avg : "+str(np.round( np.mean(MS_Tension), self.display_decimal_places)))

        # Torque 最大、平均值
        self.Torque_max.setText("Max : "+str(np.round( np.max(MS_Torsion),self.display_decimal_places)))
        self.Torque_avg.setText("Avg : "+str(np.round( np.mean(MS_Torsion), self.display_decimal_places)))

        # Temp 最大、平均值
        self.Temp_max.setText("Max : "+str(np.round( np.max(MS_Temperature),self.display_decimal_places)))
        self.Temp_avg.setText("Avg : "+str(np.round( np.mean(MS_Temperature), self.display_decimal_places)))

    def show_cnc_data(self, data):
        logger.debug(f"CNCWorker 收到數據: {data}")
        self.cnc_type.setText(f'CNC: {data["cnc_type"]}')
        self.spindle.setText(f'Spindle: {str(data["acts"])}')
        self.tool_number.setText(f'Tool: {str(data["tool_num"])}')

    def cnc_connect_failed_handler(self):
        self.Btn5_cnc_Link.setChecked(False)
    #-- CO2g 計算function --
    def UpdateCO2g(self,co2_g):
        formatted_co2 = f"{co2_g:.{IN_WINDOW_CO2_DISPLAY_DECIMALS}f}"
        self.Label_ECO.setText(formatted_co2 + "g CO2e")

    def on_slider_value_changed(self, value):
        slider_value = value
        self.ui_Filter_window.filter_label.setText(QCoreApplication.translate("Filter_Window", u"\u6ffe\u6ce2\u503c:"+str(slider_value), None))
        # print(f"Current Slider Value: {value}")
        self.filter_comparer_temp["filter_values"] = slider_value

    def on_filter_radio_toggled(self, button):
        """Radio button toggled event handler"""
      
        if button.isChecked():
            # Meanfilter_radio 均值濾波器
            if button.objectName() == "Meanfilter_radio":
                logger.debug("Meanfilter_radio")
                self.filter_comparer_temp["filter_type"] = "Meanfilter_radio"
            # Medianfilter_radio 中值濾波器
            elif button.objectName() == "Medianfilter_radio":
                # logger.debug("Medianfilter_radio")
                self.filter_comparer_temp["filter_type"] = "Medianfilter_radio"
            # Gaussianfilter_radio 高斯濾波器
            elif button.objectName() == "Gaussianfilter_radio":
                logger.debug("Gaussianfilter_radio")
                self.filter_comparer_temp["filter_type"] = "Gaussianfilter_radio"
            # MSfilter_radio Machsync濾波器
            elif button.objectName() == "MSfilter_radio":
                logger.debug("MSfilter_radio")
                self.filter_comparer_temp["filter_type"] = "MSfilter_radio"
            # Lowfilter_radio 低通濾波器
            elif button.objectName() == "Lowfilter_radio":
                logger.debug("Lowfilter_radio")
                self.filter_comparer_temp["filter_type"] = "Lowfilter_radio"
            # Highfilter_radio 高通濾波器
            elif button.objectName() == "Highfilter_radio":
                logger.debug("Highfilter_radio")
                self.filter_comparer_temp["filter_type"] = "Highfilter_radio"
            # SGfilter_radio Savitzky-Golay濾波器
            elif button.objectName() == "SGfilter_radio":
                logger.debug("SGfilter_radio") 
                self.filter_comparer_temp["filter_type"] = "SGfilter_radio"
            # MAfilter_radio 簡單移動平均
            elif button.objectName() == "MAfilter_radio":
                logger.debug("MAfilter_radio")
                self.filter_comparer_temp["filter_type"] = "MAfilter_radio"
            # Nofilter_radio 無濾波器
            else:
                self.filter_comparer_temp["filter_type"] = "Nofilter_radio"
                logger.debug("Nofilter_radio")

    # TAG: View 警示/警告顯示閥值 check_box
    def view_check_box(self,checkbox):
        # print(checkbox.objectName())
        # CF_Auto_checkBox -> C.F.[Nm]
        checkbox_map = {
            "CF_Auto_checkBox": "CF_Auto_value",
            "FxFy_Auto_checkBox": "FxFy_Auto_value",
            "Fz_Auto_checkBox": "Fz_Auto_value",
            "Torque_Auto_checkBox": "Torque_Auto_value",
            "Temp_Auto_checkBox": "Temp_Auto_value",
            "view_OpenAW_checkBox": "view_OpenAW_value",
            "view_HideRing_checkBox": "view_HideRing_value",
            "view2_HideScale_checkBox": "view2_HideScale_value"
        }

        object_name = checkbox.objectName()
        if object_name in checkbox_map:
            logger.debug(f"{object_name} changed: {checkbox.isChecked()}")
            self.view_setting_comparer_temp[checkbox_map[object_name]]["state"] = checkbox.isChecked()
            self.view_setting_value()
        else:
            logger.debug("no checkbox.objectName")
    # TAG: View 警示/警告顯示閥值設定
    def view_setting_value(self):
        logger.error("設定資料")
    

        # C.F.[Fm]
        # 設定 ReadOnly 狀態
        for CF_lineedit in ["CF_max_lineedit", "CF_min_lineedit", "CF_warning_lineedit", "CF_alarm_lineedit"]:
             line_edit_obj = getattr(self.ui_View_window, CF_lineedit)
             line_edit_obj.setReadOnly(self.view_setting_comparer_temp["CF_Auto_value"]["state"])
             InputValidator.apply_validator(line_edit_obj, "float")


        if self.view_setting_comparer_temp["CF_Auto_value"]["state"] == False:
            self.ui_View_window.CF_Auto_checkBox.setChecked(False)
            self.ui_View_window.CF_max_lineedit.setText(str(self.view_setting_comparer_temp["CF_Auto_value"]["maximum"]))
            self.ui_View_window.CF_min_lineedit.setText(str(self.view_setting_comparer_temp["CF_Auto_value"]["minimum"]))
            self.ui_View_window.CF_warning_lineedit.setText(str(self.view_setting_comparer_temp["CF_Auto_value"]["warning"]))
            self.ui_View_window.CF_alarm_lineedit.setText(str(self.view_setting_comparer_temp["CF_Auto_value"]["alarm"]))
        if self.view_setting_comparer_temp["CF_Auto_value"]["state"] == True:
            self.ui_View_window.CF_Auto_checkBox.setChecked(True)
            self.ui_View_window.CF_max_lineedit.setText("")
            self.ui_View_window.CF_min_lineedit.setText("")
            self.ui_View_window.CF_warning_lineedit.setText("")
            self.ui_View_window.CF_alarm_lineedit.setText("")

        # Fx,Fy[Nm]
        # 設定 ReadOnly 狀態
        for FxFy_lineedit in ["FxFy_pos_peak_lineedit", "FxFy_neg_peak_lineedit"]:
            line_edit_obj = getattr(self.ui_View_window, FxFy_lineedit)
            line_edit_obj.setReadOnly(self.view_setting_comparer_temp["FxFy_Auto_value"]["state"])
            InputValidator.apply_validator(line_edit_obj, "float")

        if self.view_setting_comparer_temp["FxFy_Auto_value"]["state"] == False:
            self.ui_View_window.FxFy_Auto_checkBox.setChecked(False)
            self.ui_View_window.FxFy_pos_peak_lineedit.setText(str(self.view_setting_comparer_temp["FxFy_Auto_value"]["peak_plus"]))
            self.ui_View_window.FxFy_neg_peak_lineedit.setText(str(self.view_setting_comparer_temp["FxFy_Auto_value"]["peak_minus"]))

        if self.view_setting_comparer_temp["FxFy_Auto_value"]["state"] == True:
            self.ui_View_window.FxFy_Auto_checkBox.setChecked(True)
            self.ui_View_window.FxFy_pos_peak_lineedit.setText("")
            self.ui_View_window.FxFy_neg_peak_lineedit.setText("")

        # Fz[N]
        for Fz_lineedit in ["Fz_pos_peak_lineedit", "Fz_neg_peak_lineedit", "Fz_pos_alarm_lineedit", "Fz_neg_alarm_lineedit", "Fz_pos_warning_lineedit", "Fz_neg_warning_lineedit"]:
            line_edit_obj = getattr(self.ui_View_window, Fz_lineedit)
            line_edit_obj.setReadOnly(self.view_setting_comparer_temp["Fz_Auto_value"]["state"])
            InputValidator.apply_validator(line_edit_obj, "float")

        if self.view_setting_comparer_temp["Fz_Auto_value"]["state"] == False:
            self.ui_View_window.Fz_Auto_checkBox.setChecked(False)
            self.ui_View_window.Fz_pos_peak_lineedit.setText(str(self.view_setting_comparer_temp["Fz_Auto_value"]["peak_plus"]))
            self.ui_View_window.Fz_neg_peak_lineedit.setText(str(self.view_setting_comparer_temp["Fz_Auto_value"]["peak_minus"]))
            self.ui_View_window.Fz_pos_alarm_lineedit.setText(str(self.view_setting_comparer_temp["Fz_Auto_value"]["alarm_plus"]))
            self.ui_View_window.Fz_neg_alarm_lineedit.setText(str(self.view_setting_comparer_temp["Fz_Auto_value"]["alarm_minus"]))
            self.ui_View_window.Fz_pos_warning_lineedit.setText(str(self.view_setting_comparer_temp["Fz_Auto_value"]["warning_plus"]))
            self.ui_View_window.Fz_neg_warning_lineedit.setText(str(self.view_setting_comparer_temp["Fz_Auto_value"]["warning_minus"]))

        if self.view_setting_comparer_temp["Fz_Auto_value"]["state"] == True:
            self.ui_View_window.Fz_Auto_checkBox.setChecked(True)
            self.ui_View_window.Fz_pos_peak_lineedit.setText("")
            self.ui_View_window.Fz_neg_peak_lineedit.setText("")
            self.ui_View_window.Fz_pos_alarm_lineedit.setText("")
            self.ui_View_window.Fz_neg_alarm_lineedit.setText("")
            self.ui_View_window.Fz_pos_warning_lineedit.setText("")
            self.ui_View_window.Fz_neg_warning_lineedit.setText("")

        # Torque[Nm]
        for Torque_lineedit in ["Torque_pos_peak_lineedit", "Torque_neg_peak_lineedit", "Torque_pos_alarm_lineedit", "Torque_neg_alarm_lineedit", "Torque_pos_warning_lineedit", "Torque_neg_warning_lineedit"]:
            line_edit_obj = getattr(self.ui_View_window, Torque_lineedit)
            line_edit_obj.setReadOnly(self.view_setting_comparer_temp["Torque_Auto_value"]["state"])
            InputValidator.apply_validator(line_edit_obj, "float")


        if self.view_setting_comparer_temp["Torque_Auto_value"]["state"] == False:
            self.ui_View_window.Torque_Auto_checkBox.setChecked(False)
            self.ui_View_window.Torque_pos_peak_lineedit.setText(str(self.view_setting_comparer_temp["Torque_Auto_value"]["peak_plus"]))
            self.ui_View_window.Torque_neg_peak_lineedit.setText(str(self.view_setting_comparer_temp["Torque_Auto_value"]["peak_minus"]))
            self.ui_View_window.Torque_pos_alarm_lineedit.setText(str(self.view_setting_comparer_temp["Torque_Auto_value"]["alarm_plus"]))
            self.ui_View_window.Torque_neg_alarm_lineedit.setText(str(self.view_setting_comparer_temp["Torque_Auto_value"]["alarm_minus"]))
            self.ui_View_window.Torque_pos_warning_lineedit.setText(str(self.view_setting_comparer_temp["Torque_Auto_value"]["warning_plus"]))
            self.ui_View_window.Torque_neg_warning_lineedit.setText(str(self.view_setting_comparer_temp["Torque_Auto_value"]["warning_minus"]))

        if self.view_setting_comparer_temp["Torque_Auto_value"]["state"] == True:
            self.ui_View_window.Torque_Auto_checkBox.setChecked(True)
            self.ui_View_window.Torque_pos_peak_lineedit.setText("")
            self.ui_View_window.Torque_neg_peak_lineedit.setText("")
            self.ui_View_window.Torque_pos_alarm_lineedit.setText("")
            self.ui_View_window.Torque_neg_alarm_lineedit.setText("")
            self.ui_View_window.Torque_pos_warning_lineedit.setText("")
            self.ui_View_window.Torque_neg_warning_lineedit.setText("")

        # 溫度[度C]
        for Temp_lineedit in ["Temp_max_lineedit", "Temp_min_lineedit", "Temp_warning_lineedit", "Temp_alarm_lineedit"]:
            line_edit_obj = getattr(self.ui_View_window, Temp_lineedit)
            line_edit_obj.setReadOnly(self.view_setting_comparer_temp["Temp_Auto_value"]["state"])
            InputValidator.apply_validator(line_edit_obj, "float")

        if self.view_setting_comparer_temp["Temp_Auto_value"]["state"] == False:
            self.ui_View_window.Temp_Auto_checkBox.setChecked(False)
            self.ui_View_window.Temp_max_lineedit.setText(str(self.view_setting_comparer_temp["Temp_Auto_value"]["maximum"]))
            self.ui_View_window.Temp_min_lineedit.setText(str(self.view_setting_comparer_temp["Temp_Auto_value"]["minimum"]))
            self.ui_View_window.Temp_warning_lineedit.setText(str(self.view_setting_comparer_temp["Temp_Auto_value"]["warning"]))
            self.ui_View_window.Temp_alarm_lineedit.setText(str(self.view_setting_comparer_temp["Temp_Auto_value"]["alarm"]))

        if self.view_setting_comparer_temp["Temp_Auto_value"]["state"] == True:
            self.ui_View_window.Temp_Auto_checkBox.setChecked(True)
            self.ui_View_window.Temp_max_lineedit.setText("")
            self.ui_View_window.Temp_min_lineedit.setText("")
            self.ui_View_window.Temp_warning_lineedit.setText("")
            self.ui_View_window.Temp_alarm_lineedit.setText("")

        # 警示/警告
        self.ui_View_window.view_OpenAW_checkBox.setChecked(self.view_setting_comparer_temp["view_OpenAW_value"]["state"])

        # 其他顯示

        # 隱藏 Fz/Torque
        self.ui_View_window.view_HideRing_checkBox.setChecked(self.view_setting_comparer_temp["view_HideRing_value"]["state"])

        # 隱藏 3D 輔助線
        self.ui_View_window.view2_HideScale_checkBox.setChecked(self.view_setting_comparer_temp["view2_HideScale_value"]["state"])

        # 設置初始值
        color_spectrum = self.view_setting_comparer_temp.get("color_spectrum", 0)
        if color_spectrum == 0:
            self.ui_View_window.ColorSpectrum_radio.setChecked(True)
        elif color_spectrum == 1:
            self.ui_View_window.ColorSpectrum_radio_2.setChecked(True)
        elif color_spectrum == 2:
            self.ui_View_window.ColorSpectrum_radio_3.setChecked(True)
        elif color_spectrum == 3:
            self.ui_View_window.ColorSpectrum_radio_4.setChecked(True)
    # 警示/警告燈號 #TODO: 不只底部狀態列有警示色，要檢查其他
    def update_safety_status(self, status:StressSafetyStatus):
        self.sigUpdateSafetyStatus.emit(status)


    def on_view_radio_toggled(self, button, checked):
        if checked:
            # 確保只有被選中的按鈕是選中狀態
            for radio in [self.ui_View_window.ColorSpectrum_radio,
                        self.ui_View_window.ColorSpectrum_radio_2,
                        self.ui_View_window.ColorSpectrum_radio_3,
                        self.ui_View_window.ColorSpectrum_radio_4]:
                radio.setChecked(radio == button)
            
            # 根據選中的按鈕更新視圖
            if button.objectName() ==  "ColorSpectrum_radio":
                self.view_setting_comparer_temp["color_spectrum"] = 0
            elif button.objectName() == "ColorSpectrum_radio_2":
                self.view_setting_comparer_temp["color_spectrum"] = 1
            elif button.objectName() == "ColorSpectrum_radio_3":
                self.view_setting_comparer_temp["color_spectrum"] = 2
            elif button.objectName() == "ColorSpectrum_radio_4":
                self.view_setting_comparer_temp["color_spectrum"] = 3


    def toggle_editable(self, combo_box, index):
        """切換 ComboBox 編輯模式"""
        combo_box.setEditable(combo_box.itemText(index) == "Custom Value")
        if combo_box.isEditable():
            combo_box.clearEditText()
            combo_box.setFocus()

    def populate_combobox(self, combo_box, values_dict):
        """填充 ComboBox 選項"""
        combo_box.addItems(values_dict.keys())

    def initialize_combobox(self, comboBox, data_dict, key):
        """初始化 ComboBox，根據 data_dict 的值與 key 來設定對應選項"""
        current_value = self.CO2_data.get(key)

        # 嘗試根據值找到對應的選項
        for item, value in data_dict.items():
            if value == current_value:
                comboBox.setCurrentText(item)
                return

        # 如果找不到匹配的選項，設置為自定義值
        comboBox.setCurrentText("Custom Value")
        comboBox.setEditable(True)
        comboBox.setEditText(str(current_value))
        
    def get_CO2e_fields(self):
        """返回 CO2e 參數對應的 LineEdit 元件"""
        return {
            "MS_CO2_zc": self.ui_CO2e_window.CO2_zc_lineEdit,
            "MS_CO2_Dc": self.ui_CO2e_window.CO2_Dc_lineEdit,
            "MS_CO2_vc": self.ui_CO2e_window.CO2_vc_lineEdit,
            "MS_CO2_fz": self.ui_CO2e_window.CO2_fz_lineEdit,
            "MS_CO2_ap": self.ui_CO2e_window.CO2_ap_lineEdit,
            "MS_CO2_ae": self.ui_CO2e_window.CO2_ae_lineEdit,
            "MS_CO2_n": self.ui_CO2e_window.CO2_n_lineEdit,
            "MS_CO2_vf": self.ui_CO2e_window.CO2_vf_lineEdit,
            "MS_CO2_Q": self.ui_CO2e_window.CO2_Q_lineEdit,
            "MS_CO2_Pc": self.ui_CO2e_window.CO2_pc_lineEdit,
            "MS_CO2_Pb": self.ui_CO2e_window.CO2_Pb_lineEdit,
        }

    def reset_text_file_name(self):
        """重設 text file 名稱"""
         
        self.msra_file_name= "Record_" + time.strftime("%Y%m%d_%H%M%S",time.localtime()) + ".msra"
        self.ui_Setting_window.txtname_lineEdit.setText(str(self.msra_file_name))

    def set_text_file_name(self):
        """設定 text file 名稱並儲存設定"""
        # 🔥 儲存前先驗證所有設定欄位
        is_valid, error_message = InputValidator.validate_setting_fields(self.ui_Setting_window)
        if not is_valid:
            # 驗證失敗，顯示錯誤訊息
            logger.error(f"設定欄位驗證失敗: {error_message}")
            self.show_Remind_window(error_message)
            return  # 不繼續執行儲存
        
        # 驗證成功，繼續原有的儲存邏輯
        logger.info("設定欄位驗證成功，開始儲存")

        """設定 text file 名稱"""
        msra_file_path = self.ui_Setting_window.FolderPath_lineEdit.text()
        msra_file_name = self.ui_Setting_window.txtname_lineEdit.text()
        save_raw_data = self.ui_Setting_window.save_rawdata_checkBox.isChecked()

        logger.info(f"設定 text path 名稱: {msra_file_path}")
        logger.info(f"設定 text file 名稱: {msra_file_name}")
        logger.info(f"設定 save_raw_data: {save_raw_data}")
        
        data = {
            "msra_file_path": msra_file_path,
            "msra_file_name": msra_file_name,
            "save_raw_data": save_raw_data
        }

        self.sigSaveFileData.emit(data)

        self.close_window("Setting_window")



# Tab 鍵焦點切換
    def switch_focus(self):
        """ 手動切換按鈕焦點 """
        # 先清除當前按鈕的樣式
        self.tab_buttons[self.tab_current_index].setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
        "QPushButton:hover { border: none; color: #7AFEC6; }")
        # 更新索引到下一個按鈕
        self.tab_current_index = (self.tab_current_index + 1) % len(self.tab_buttons)
        # 設定新按鈕的焦點和樣式
        self.tab_buttons[self.tab_current_index].setFocus()
        self.tab_buttons[self.tab_current_index].setStyleSheet(u"QPushButton { border: none; color: #7AFEC6; }\n"
        "QPushButton:hover { border: none; color: #7AFEC6; }")
        # logger.info(f"焦點切換到: {self.tab_buttons[self.tab_current_index].text()}")

    def keyPressEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key_Tab:
            self.switch_focus()
            event.accept()
        elif event.key() in (Qt.Key_Return, Qt.Key_Enter):
            # 觸發目前選取的按鈕
            selected_button = self.get_selected_button()
            if selected_button:
                # logger.info(f"當前選取的按鈕是: {selected_button.text()}")
                selected_button.click()
        else:
            super().keyPressEvent(event)

    def get_selected_button(self):
        """ 回傳目前擁有焦點的按鈕 """
        for btn in self.tab_buttons:
            if btn.hasFocus():
                return btn
        return None

    def update_button_styles(self):
        """ 更新按鈕的文字顏色來顯示選取狀態 """
        for i, btn in enumerate(self.tab_buttons):
            if i == self.tab_current_index:
                btn.setStyleSheet(u"QPushButton { border: none; color: #7AFEC6; }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")
                # logger.info(f"更新按鈕樣式: {btn.text()} (焦點)")
            else:
                btn.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }\n"
"QPushButton:hover { border: none; color: #7AFEC6; }")

