import os
from dotenv import load_dotenv

# 載入 .env 檔案
load_dotenv()

data_queue = None
scanToolIP_thread=False # 這是要給toollist查看連線
currentIP = None
MS_BendingXY = None

# 網路設備連線設定
NETWORK_DEVICE_URL = os.getenv('NETWORK_DEVICE_URL')
NETWORK_DEVICE_USERNAME = os.getenv('NETWORK_DEVICE_USERNAME')
NETWORK_DEVICE_PASSWORD = os.getenv('NETWORK_DEVICE_PASSWORD')

# 檢查必要的環境變數是否存在
if not all([NETWORK_DEVICE_URL, NETWORK_DEVICE_USERNAME, NETWORK_DEVICE_PASSWORD]):
    raise ValueError("請確認 .env 檔案中已設定 NETWORK_DEVICE_URL、NETWORK_DEVICE_USERNAME 和 NETWORK_DEVICE_PASSWORD")



# 系統預設值
DEFAULT_DISPLAY_DECIMALS = 3

IN_WINDOW_CO2_DISPLAY_DECIMALS = 3  # 主畫面 CO2 顯示專用
CO2_SETTING_DISPLAY_DECIMALS = 3    # CO2 設定項目專用

DEFAULT_TOOL_DATA = {
    'id': -1,
    'toolname': "default_name",
    'toolip': None,
    'toolmac': None,
    'sample_rate': 10000,
    'tare_xv': 2.5,
    'tare_yv': 2.5,
    'tare_zv': 2.5,
    'tare_tv': 2.5,
    'Linear_x': 1446.0,
    'Linear_y': 1465.0,
    'Linear_z': 31094.0,
    'Linear_t': 1.0,
    'tare_gx': 0,
    'tare_gy': 0,
    'tare_gz': 0,
    'auto_record_enabled': 0, 
    'auto_pre_record_seconds': 0,
    'auto_record_seconds': 10,
    'auto_max_record_count': 100,
    'auto_cf_enabled': 0,
    'auto_fz_enabled': 0,
    'auto_t_enabled': 0,
    'auto_cf_threshold': 0,
    'auto_fz_threshold': 0,
    'auto_t_threshold': 0,
    'Lc': 0.136,
    'Hl': 1.1,
    'Kl': 0.04,
    'Bx_COMP': 1446.0,
    'By_COMP': 1465.0,
    'Bz_COMP': 31094.0,
    'Bt_COMP': 1.0,
    'localtime': None,
    'CO2_id': None,
}

DEFAULT_CO2_DATA = {
    'MS_CO2_K': 5,
    'MS_CO2_zc': 4, 
    'MS_CO2_Dc': 6.0, 
    'MS_CO2_vc': 100.0,
    'MS_CO2_fz': 0.05, 
    'MS_CO2_ap': 1.0, 
    'MS_CO2_ae': 3.0, 
    'MS_CO2_n': 5305.2,
    'MS_CO2_vf': 1061.04, 
    'MS_CO2_Q': 3.183, 
    'MS_CO2_Pc': 1.0, 'MS_CO2_Pb': 0.275, # unit: kW  
    #估算：主軸額定最大功率5.5kW，空轉估額定最大功率之5%；輕加工主軸功率約1kW以下
    'MS_CO2_EF': 0.474 , # unit: kgCO2/kWh
    'init_status' : 0
}

CO2_K_VALUES = {
    "Alloy Steel": 5,
    "Titanium Alloy": 7,
    "Structural Steel": 10,
    "Cast Steel": 15,
    "Cast Iron": 30,
    "Aluminum Alloy": 60,
    "Custom Value": None
}

CO2_EF_VALUES = {
    "0.474": 0.474,  # for Taiwan #113年度台電公告值(2025/4/14公告)
    "0.510": 0.510,  # for China
    "Custom Value": None
}

DEFAULT_FILTER = {
    "filter_type": "Nofilter_radio",
    "filter_values": 1,
    "Lowfilter_edit": 999,
    "Highfilter_edit": 1,
    "SGfilter_edit": 4,
    "MAfilter_edit": 1
}

# Record Script Config
RECORD_SCRIPT_DEFAULT_CONFIG ={
    "connection_retry_attempts": 3,
    "connection_retry_interval": 0.5,
    "script_recordings_directory": "script_recordings",
    "connection_timeout": 10.0,
    "user_action_on_error": True, # if True, user will be asked to choose action on error
    "action_on_error": "skip", # used if user_action_on_error is False, may be ["skip", "stop"]
    "user_action_timeout": 30,
}
