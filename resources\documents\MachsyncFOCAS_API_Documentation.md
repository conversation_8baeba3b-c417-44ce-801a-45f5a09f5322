# MachsyncFOCAS Python API 說明文件

## 概述

MachsyncFOCAS 是一個用於與 FANUC CNC 機器通信的 Python 庫，基於 FANUC FOCAS 庫開發。該庫提供了完整的 CNC 數據讀取和機器控制功能。

## 安裝與初始化

### 初始化
```python
from MachsyncFOCAS import MachsyncFocas, Status

# 創建實例
cnc = MachsyncFocas("Fwlib64.dll")  # 請替換為實際的 DLL 路徑
```

## 核心功能

### 1. 連接管理

#### CNC_Connect(ip, port=8193, timeout=3)
連接到 CNC 機器。

**參數：**
- `ip` (str): CNC 機器的 IP 地址
- `port` (int): 連接端口，預設為 8193
- `timeout` (int): 連接超時時間（秒），預設為 3

**返回值：**
- `tuple`: (status_code, handle)
  - `status_code`: 狀態碼，0 表示成功
  - `handle`: CNC 句柄

**範例：**
```python
ret, handle = cnc.CNC_Connect("*************")
if ret == Status.EW_OK:
    print(f"成功連接到 CNC，句柄：{handle}")
```

#### CNC_Disconnect(handle)
斷開與 CNC 機器的連接。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `int`: 狀態碼

**範例：**
```python
cnc.CNC_Disconnect(handle)
```

### 2. 機器控制

#### CNC_Start(handle)
啟動 CNC 循環。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `int`: 狀態碼

#### CNC_Stop(handle)
執行 CNC 外部重置。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `int`: 狀態碼

### 3. 動態數據讀取

#### CNC_ReadDynamicData(libHandle)
讀取 CNC 動態數據，包含位置、速度、程式信息等。

**參數：**
- `libHandle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, dynamic_data)
  - `status_code`: 狀態碼
  - `dynamic_data`: DynamicData 對象，包含：
    - `axis`: 軸數
    - `alarm`: 警報狀態
    - `prgnum`: 當前程式號
    - `prgmnum`: 主程式號
    - `seqnum`: 當前序列號
    - `actf`: 實際進給速度
    - `acts`: 實際主軸速度
    - `absolutePos`: 絕對位置 (Point 結構)
    - `relativePos`: 相對位置 (Point 結構)
    - `machinePos`: 機器位置 (Point 結構)

**範例：**
```python
status, dynamic_data = cnc.CNC_ReadDynamicData(handle)
if status == Status.EW_OK:
    print(f"軸數：{dynamic_data.axis}")
    print(f"程式號：{dynamic_data.prgnum}")
    print(f"絕對位置 X：{dynamic_data.absolutePos.x}")
```

### 4. 軸信息讀取

#### CNC_ReadAxisNames(handle)
讀取 CNC 軸名稱。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, axis_num, axis_names)
  - `status_code`: 狀態碼
  - `axis_num`: 實際軸數
  - `axis_names`: 軸名稱列表

**範例：**
```python
status, axis_num, axis_names = cnc.CNC_ReadAxisNames(handle)
if status == Status.EW_OK:
    print(f"軸數：{axis_num}")
    print(f"軸名稱：{axis_names}")
```

### 5. G代碼讀取

#### CNC_ReadGCode(handle)
讀取 CNC G代碼數據。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, num_gcd, gcode_list)
  - `status_code`: 狀態碼
  - `num_gcd`: G代碼數量
  - `gcode_list`: G代碼信息列表，每個元素包含：
    - `index`: 索引
    - `group`: 組別
    - `flag`: 標誌值
    - `flag_info`: 標誌解析信息
    - `code`: G代碼字串

**範例：**
```python
status, num_gcd, gcode_list = cnc.CNC_ReadGCode(handle)
if status == Status.EW_OK:
    for gcode in gcode_list[:5]:
        print(f"G代碼：{gcode['code']}, 組別：{gcode['group']}")
```

### 6. 速度數據讀取

#### CNC_ReadSpeedData(handle)
讀取 CNC 速度數據，包含進給速度和主軸速度的詳細信息。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, speed_data)
  - `status_code`: 狀態碼
  - `speed_data`: 速度數據字典，包含：
    - `actual_feedrate`: 實際進給速度信息
    - `actual_spindle_speed`: 實際主軸速度信息

**範例：**
```python
status, speed_data = cnc.CNC_ReadSpeedData(handle)
if status == Status.EW_OK:
    feedrate = speed_data['actual_feedrate']
    print(f"進給速度：{feedrate['data']} {feedrate['unit']}")
```

### 7. 位置數據讀取

#### CNC_ReadPositionData(handle)
讀取 CNC 位置數據，包含所有軸的詳細位置信息。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, data_num, position_data)
  - `status_code`: 狀態碼
  - `data_num`: 實際讀取的軸數
  - `position_data`: 位置數據字典，按軸組織

**範例：**
```python
status, data_num, position_data = cnc.CNC_ReadPositionData(handle)
if status == Status.EW_OK:
    for axis_key, axis_info in position_data.items():
        abs_pos = axis_info['absolute']
        print(f"{axis_key}: {abs_pos['data']} {abs_pos['unit']}")
```

### 8. 系統信息讀取

#### CNC_ReadSystemInfo(handle)
讀取 CNC 系統信息。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, system_info)
  - `status_code`: 狀態碼
  - `system_info`: 系統信息字典，包含：
    - `addinfo`: 附加信息原始值
    - `addinfo_parsed`: 解析後的附加信息
    - `max_axis`: 最大軸數
    - `cnc_type`: CNC 類型原始值
    - `cnc_type_parsed`: 解析後的 CNC 類型
    - `mt_type`: 機器類型原始值
    - `mt_type_parsed`: 解析後的機器類型
    - `series`: 系列
    - `version`: 版本
    - `axes`: 軸信息

**範例：**
```python
status, system_info = cnc.CNC_ReadSystemInfo(handle)
if status == Status.EW_OK:
    print(f"CNC類型：{system_info['cnc_type_parsed']}")
    print(f"機器類型：{system_info['mt_type_parsed']}")
```

### 9. 命令數據讀取

#### CNC_ReadModalDataExceptGCode(handle)
讀取除G代碼外的模態數據。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, num_commands, command_list)
  - `status_code`: 狀態碼
  - `num_commands`: 命令數量
  - `command_list`: 命令信息列表

#### CNC_ReadCommandedData(handle)
讀取所有命令數據。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, num_commands, command_list)
  - `status_code`: 狀態碼
  - `num_commands`: 命令數量
  - `command_list`: 命令信息列表

#### CNC_ReadCommandedDataConcerningAxis(handle)
讀取與軸相關的所有命令數據。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, num_commands, command_list)
  - `status_code`: 狀態碼
  - `num_commands`: 命令數量
  - `command_list`: 命令信息列表

**範例：**
```python
status, num_commands, command_list = cnc.CNC_ReadModalDataExceptGCode(handle)
if status == Status.EW_OK:
    for cmd in command_list[:5]:
        print(f"命令：{cmd['address']}, 值：{cmd['command_value']}")
```

### 10. 計時器數據讀取

#### CNC_ReadTimerData(handle, timer_type=0)
讀取 CNC 計時器數據。

**參數：**
- `handle`: CNC 句柄
- `timer_type`: 計時器類型
  - 0: 獲取日期
  - 1: 獲取時間

**返回值：**
- `tuple`: (status_code, timer_data)
  - `status_code`: 狀態碼
  - `timer_data`: 計時器數據字典

**範例：**
```python
# 讀取日期
status, timer_data = cnc.CNC_ReadTimerData(handle, 0)
if status == Status.EW_OK:
    date = timer_data['date']
    print(f"日期：{date['year']}-{date['month']}-{date['date']}")

# 讀取時間
status, timer_data = cnc.CNC_ReadTimerData(handle, 1)
if status == Status.EW_OK:
    time = timer_data['time']
    print(f"時間：{time['hour']}:{time['minute']}:{time['second']}")
```

### 11. 工具偏移數據讀取

#### CNC_ReadToolOffsetData(handle, s_number=1, e_number=5)
讀取工具偏移數據。

**參數：**
- `handle`: CNC 句柄
- `s_number`: 開始偏移編號，預設為 1
- `e_number`: 結束偏移編號，預設為 5

**返回值：**
- `tuple`: (status_code, tool_offset_data)
  - `status_code`: 狀態碼
  - `tool_offset_data`: IODBTO_1_3 結構實例

**範例：**
```python
status, tool_offset_data = cnc.CNC_ReadToolOffsetData(handle)
if status == Status.EW_OK:
    print(f"開始偏移編號：{tool_offset_data.datano_s}")
    print(f"結束偏移編號：{tool_offset_data.datano_e}")
```

#### CNC_ReadToolOffsetInfo(handle)
讀取工具偏移信息。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, tool_offset_info)
  - `status_code`: 狀態碼
  - `tool_offset_info`: 工具偏移信息字典，包含：
    - `ofs_type`: 工具偏移記憶體類型描述
    - `use_no`: 可用的工具偏移數量

**範例：**
```python
status, tool_offset_info = cnc.CNC_ReadToolOffsetInfo(handle)
if status == Status.EW_OK:
    print(f"偏移類型：{tool_offset_info['ofs_type']}")
    print(f"可用工具偏移數量：{tool_offset_info['use_no']}")
```

### 12. 工具壽命數據讀取

#### CNC_ReadCurrentToolData(handle)
讀取當前工具數據。

**參數：**
- `handle`: CNC 句柄

**返回值：**
- `tuple`: (status_code, tool_data)
  - `status_code`: 狀態碼
  - `tool_data`: 工具數據字典，包含：
    - `groupno`: 工具組號
    - `tool_num`: 工具號
    - `h_code`: H代碼
    - `d_code`: D代碼
    - `tool_inf`: 工具信息數值
    - `tool_inf_desc`: 工具信息描述
      - 0: (refer to the following)
      - 1: This tool is registered(available).
      - 2: This tool has expired.
      - 3: This tool was skipped.

**範例：**
```python
status, tool_data = cnc.CNC_ReadCurrentToolData(handle)
if status == Status.EW_OK:
    print(f"工具號：{tool_data['tool_num']}")
    print(f"工具狀態：{tool_data['tool_inf_desc']}")
    
    # 根據工具狀態進行不同處理
    if tool_data['tool_inf'] == 1:
        print("工具可用")
    elif tool_data['tool_inf'] == 2:
        print("工具已過期，需要更換")
    elif tool_data['tool_inf'] == 3:
        print("工具被跳過")
```

## 工具函數

### GetStatusDescription(status_code)
獲取狀態碼的詳細描述。

**參數：**
- `status_code`: 狀態碼

**返回值：**
- `str`: 狀態描述字串

**範例：**
```python
description = cnc.GetStatusDescription(Status.EW_OK)
print(description)
```

### GetAlarmDescription(alarm_value)
解析32位元警報狀態。

**參數：**
- `alarm_value`: 32位元整數，包含警報狀態位元

**返回值：**
- `dict`: 包含所有警報狀態的字典

**範例：**
```python
alarm_status = cnc.GetAlarmDescription(alarm_value)
for bit, (alarm_type, description, is_set) in alarm_status.items():
    if is_set:
        print(f"位元 {bit}: {alarm_type} - {description}")
```

## 狀態碼定義

### 成功狀態
- `EW_OK = 0`: 正常終止

### 錯誤狀態
- `EW_PROTOCOL = -17`: 協議錯誤
- `EW_SOCKET = -16`: Windows socket 錯誤
- `EW_NODLL = -15`: DLL 不存在錯誤
- `EW_BUS = -11`: 總線錯誤
- `EW_SYSTEM2 = -10`: 系統錯誤
- `EW_HSSB = -9`: HSSB 通信錯誤
- `EW_HANDLE = -8`: Windows 庫句柄錯誤
- `EW_VERSION = -7`: CNC/PMC 版本不匹配
- `EW_UNEXP = -6`: 異常錯誤
- `EW_SYSTEM = -5`: 系統錯誤
- `EW_PARITY = -4`: 共享 RAM 奇偶校驗錯誤
- `EW_MMCSYS = -3`: emm386 或 mmcsys 安裝錯誤
- `EW_RESET = -2`: 重置或停止發生錯誤
- `EW_BUSY = -1`: 忙碌錯誤

更多狀態碼請參考 `Status` 枚舉類別。

## 完整使用範例

```python
from MachsyncFOCAS import MachsyncFocas, Status

try:
    # 創建實例
    cnc = MachsyncFocas("Fwlib64.dll")
    
    # 連接到 CNC
    ret, handle = cnc.CNC_Connect("*************")
    if ret == Status.EW_OK:
        print(f"成功連接到 CNC，句柄：{handle}")
        
        # 讀取動態數據
        status, dynamic_data = cnc.CNC_ReadDynamicData(handle)
        if status == Status.EW_OK:
            print(f"軸數：{dynamic_data.axis}")
            print(f"程式號：{dynamic_data.prgnum}")
            print(f"絕對位置 X：{dynamic_data.absolutePos.x}")
        
        # 讀取系統信息
        status, system_info = cnc.CNC_ReadSystemInfo(handle)
        if status == Status.EW_OK:
            print(f"CNC類型：{system_info['cnc_type_parsed']}")
            print(f"機器類型：{system_info['mt_type_parsed']}")
        
        # 讀取當前工具數據
        status, tool_data = cnc.CNC_ReadCurrentToolData(handle)
        if status == Status.EW_OK:
            print(f"工具號：{tool_data['tool_num']}")
            print(f"工具狀態：{tool_data['tool_inf_desc']}")
        
        # 斷開連接
        cnc.CNC_Disconnect(handle)
    else:
        print(f"連接失敗，錯誤碼：{ret}")
        print(f"錯誤描述：{cnc.GetStatusDescription(ret)}")

except Exception as e:
    print(f"錯誤：{e}")
```

## 注意事項

1. 確保 FANUC FOCAS 庫已正確安裝
2. 檢查網路連接和 CNC 機器狀態
3. 處理所有可能的錯誤狀態
4. 正確釋放 CNC 句柄
5. 注意數據類型和單位轉換

## 版本信息

- 支援的 CNC 系列：Series 0i, 15i, 16i, 18i, 21i, 30i, 31i, 32i, 35i
- 支援的通信方式：Ethernet, HSSB
- Python 版本要求：3.6+
- 作業系統：Windows 