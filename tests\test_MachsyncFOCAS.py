# -*- coding: utf-8 -*-
import unittest
import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock, call
from ctypes import c_ushort, c_short, c_longlong, c_int, c_char_p, byref
import ctypes

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from MachsyncFOCAS import (
    MachsyncFocas, Status, Point, DynamicData, FAXIS, ODBDY2_1,
    ODBEXAXISNAME, ODBGCD, ODBGCD_data, ODBSPEED, SPEEDELM,
    ODBPOS, POSELM, POSELMALL, ODBCMD, ODBCMD_data,
    ODBSYS, ODBTLINF, IODBTIMER, TIMER_DATE, TIMER_TIME,
    IODBTO_1_3, OFS_3
)


class TestMachsyncFocas(unittest.TestCase):
    """MachsyncFocas 類別的單元測試"""
    
    def setUp(self):
        """測試前的設置"""
        # 創建一個臨時的 DLL 文件路徑
        self.temp_dir = tempfile.mkdtemp()
        self.dll_path = os.path.join(self.temp_dir, "Fwlib64.dll")
        
        # 創建一個假的 DLL 文件
        with open(self.dll_path, 'w') as f:
            f.write("fake dll content")
        
        # 模擬 ctypes.WinDLL
        self.mock_dll = Mock()
        
    def tearDown(self):
        """測試後的清理"""
        # 清理臨時目錄
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @patch('ctypes.WinDLL')
    def test_init_success(self, mock_win_dll):
        """測試成功初始化"""
        mock_win_dll.return_value = self.mock_dll
        
        cnc = MachsyncFocas(self.dll_path)
        
        self.assertEqual(cnc.dllPath, self.dll_path)
        self.assertEqual(cnc.focas, self.mock_dll)
        mock_win_dll.assert_called_once_with(self.dll_path)
    
    def test_init_dll_not_found(self):
        """測試 DLL 文件不存在的情況"""
        non_existent_dll = "non_existent.dll"
        
        with self.assertRaises(FileNotFoundError) as context:
            MachsyncFocas(non_existent_dll)
        
        self.assertIn("找不到 DLL 文件", str(context.exception))
    
    @patch('ctypes.WinDLL')
    def test_cnc_connect_success(self, mock_win_dll):
        """測試成功連接到 CNC"""
        mock_win_dll.return_value = self.mock_dll
        
        # 模擬成功的連接
        self.mock_dll.cnc_allclibhndl3.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        ret, handle = cnc.CNC_Connect("192.168.1.100")
        
        self.assertEqual(ret, Status.EW_OK)
        self.mock_dll.cnc_allclibhndl3.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_connect_failure(self, mock_win_dll):
        """測試連接失敗的情況"""
        mock_win_dll.return_value = self.mock_dll
        
        # 模擬連接失敗
        self.mock_dll.cnc_allclibhndl3.return_value = Status.EW_SOCKET
        
        cnc = MachsyncFocas(self.dll_path)
        ret, handle = cnc.CNC_Connect("192.168.1.100")
        
        self.assertEqual(ret, Status.EW_SOCKET)
    
    @patch('ctypes.WinDLL')
    def test_cnc_disconnect(self, mock_win_dll):
        """測試斷開連接"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_freelibhndl.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        ret = cnc.CNC_Disconnect(123)
        
        self.assertEqual(ret, Status.EW_OK)
        self.mock_dll.cnc_freelibhndl.assert_called_once_with(123)
    
    def test_get_status_description(self):
        """測試狀態描述獲取"""
        # 測試已知狀態碼
        desc = MachsyncFocas.GetStatusDescription(Status.EW_OK)
        self.assertIn("Normal termination", desc)
        
        # 測試未知狀態碼
        desc = MachsyncFocas.GetStatusDescription(999)
        self.assertIn("No such status code", desc)
    
    @patch('ctypes.WinDLL')
    def test_get_alarm_description(self, mock_win_dll):
        """測試警報描述解析"""
        mock_win_dll.return_value = self.mock_dll
        cnc = MachsyncFocas(self.dll_path)
        
        # 測試無警報
        alarm_status = cnc.GetAlarmDescription(0)
        self.assertIsInstance(alarm_status, dict)
        self.assertEqual(len(alarm_status), 32)
        
        # 測試有警報
        alarm_value = 1  # 位元 0 設為 1
        alarm_status = cnc.GetAlarmDescription(alarm_value)
        self.assertTrue(alarm_status[0][2])  # 位元 0 應該為 True
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_dynamic_data_success(self, mock_win_dll):
        """測試成功讀取動態數據"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rddynamic2.return_value = Status.EW_OK
        
        # 創建模擬的 ODBDY2_1 結構
        mock_dyn_data = ODBDY2_1()
        mock_dyn_data.axis = 3
        mock_dyn_data.alarm = 0
        mock_dyn_data.prgnum = 1000
        mock_dyn_data.prgmnum = 1000
        mock_dyn_data.seqnum = 100
        mock_dyn_data.actf = 500
        mock_dyn_data.acts = 1000
        
        # 設置位置數據
        mock_dyn_data.pos.absolute[0] = 1000
        mock_dyn_data.pos.absolute[1] = 2000
        mock_dyn_data.pos.absolute[2] = 3000
        mock_dyn_data.pos.relative[0] = 1100
        mock_dyn_data.pos.relative[1] = 2100
        mock_dyn_data.pos.relative[2] = 3100
        mock_dyn_data.pos.machine[0] = 1200
        mock_dyn_data.pos.machine[1] = 2200
        mock_dyn_data.pos.machine[2] = 3200
        
        # 模擬 cnc_rddynamic2 函數填充數據
        def mock_cnc_rddynamic2(handle, axis, length, data_ptr):
            # 將模擬數據複製到提供的結構指針
            ctypes.memmove(data_ptr, byref(mock_dyn_data), ctypes.sizeof(ODBDY2_1))
            return 0  # 返回 0 表示成功
        
        self.mock_dll.cnc_rddynamic2.side_effect = mock_cnc_rddynamic2
        
        cnc = MachsyncFocas(self.dll_path)
        status, dynamic_data = cnc.CNC_ReadDynamicData(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.assertIsInstance(dynamic_data, DynamicData)
        self.assertEqual(dynamic_data.axis, 3)
        self.assertEqual(dynamic_data.prgnum, 1000)
        self.assertEqual(dynamic_data.absolutePos.x, 1000)
        self.assertEqual(dynamic_data.absolutePos.y, 2000)
        self.assertEqual(dynamic_data.absolutePos.z, 3000)
        self.mock_dll.cnc_rddynamic2.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_dynamic_data_failure(self, mock_win_dll):
        """測試讀取動態數據失敗"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rddynamic2.return_value = Status.EW_HANDLE
        
        cnc = MachsyncFocas(self.dll_path)
        status, dynamic_data = cnc.CNC_ReadDynamicData(123)
        
        self.assertEqual(status, Status.EW_HANDLE)
        self.assertIsNone(dynamic_data)
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_axis_names_success(self, mock_win_dll):
        """測試成功讀取軸名稱"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_exaxisname.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, axis_num, axis_names = cnc.CNC_ReadAxisNames(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_exaxisname.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_gcode_success(self, mock_win_dll):
        """測試成功讀取 G 代碼"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdgcode.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, num_gcd, gcode_list = cnc.CNC_ReadGCode(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdgcode.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_speed_data_success(self, mock_win_dll):
        """測試成功讀取速度數據"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdspeed.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, speed_data = cnc.CNC_ReadSpeedData(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdspeed.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_position_data_success(self, mock_win_dll):
        """測試成功讀取位置數據"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdposition.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, data_num, position_data = cnc.CNC_ReadPositionData(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdposition.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_system_info_success(self, mock_win_dll):
        """測試成功讀取系統信息"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_sysinfo.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, system_info = cnc.CNC_ReadSystemInfo(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_sysinfo.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_parse_addinfo(self, mock_win_dll):
        """測試 addinfo 解析"""
        mock_win_dll.return_value = self.mock_dll
        cnc = MachsyncFocas(self.dll_path)
        
        # 測試 addinfo = 0
        result = cnc._parse_addinfo(0)
        self.assertIn("without loader control function", result)
        self.assertIn("not an i Series CNC", result)
        
        # 測試 addinfo = 1 (位元 0 設為 1)
        result = cnc._parse_addinfo(1)
        self.assertIn("with loader control function", result)
    
    @patch('ctypes.WinDLL')
    def test_parse_cnc_type(self, mock_win_dll):
        """測試 CNC 類型解析"""
        mock_win_dll.return_value = self.mock_dll
        cnc = MachsyncFocas(self.dll_path)
        
        # 測試已知類型
        result = cnc._parse_cnc_type("15")
        self.assertEqual(result, "Series 15/15i")
        
        # 測試未知類型
        result = cnc._parse_cnc_type("99")
        self.assertIn("Unknown", result)
    
    @patch('ctypes.WinDLL')
    def test_parse_mt_type(self, mock_win_dll):
        """測試機器類型解析"""
        mock_win_dll.return_value = self.mock_dll
        cnc = MachsyncFocas(self.dll_path)
        
        # 測試已知類型
        result = cnc._parse_mt_type(" M")
        self.assertEqual(result, "Machining center")
        
        # 測試未知類型
        result = cnc._parse_mt_type("XX")
        self.assertIn("Unknown", result)
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_modal_data_except_gcode_success(self, mock_win_dll):
        """測試成功讀取除 G 代碼外的模態數據"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdcommand.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, num_commands, command_list = cnc.CNC_ReadModalDataExceptGCode(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdcommand.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_commanded_data_success(self, mock_win_dll):
        """測試成功讀取所有命令數據"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdcommand.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, num_commands, command_list = cnc.CNC_ReadCommandedData(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdcommand.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_commanded_data_concerning_axis_success(self, mock_win_dll):
        """測試成功讀取與軸相關的命令數據"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdcommand.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, num_commands, command_list = cnc.CNC_ReadCommandedDataConcerningAxis(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdcommand.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_timer_data_date_success(self, mock_win_dll):
        """測試成功讀取計時器數據（日期）"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_gettimer.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, timer_data = cnc.CNC_ReadTimerData(123, timer_type=0)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_gettimer.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_timer_data_time_success(self, mock_win_dll):
        """測試成功讀取計時器數據（時間）"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_gettimer.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, timer_data = cnc.CNC_ReadTimerData(123, timer_type=1)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_gettimer.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_tool_offset_data_success(self, mock_win_dll):
        """測試成功讀取工具偏移數據"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdtofsr.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, tool_offset_data = cnc.CNC_ReadToolOffsetData(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdtofsr.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_read_tool_offset_info_success(self, mock_win_dll):
        """測試成功讀取工具偏移信息"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_rdtofsinfo.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        status, tool_offset_info = cnc.CNC_ReadToolOffsetInfo(123)
        
        self.assertEqual(status, Status.EW_OK)
        self.mock_dll.cnc_rdtofsinfo.assert_called_once()
    
    @patch('ctypes.WinDLL')
    def test_cnc_stop(self, mock_win_dll):
        """測試 CNC 停止"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_reset.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        ret = cnc.CNC_Stop(123)
        
        self.assertEqual(ret, Status.EW_OK)
        self.mock_dll.cnc_reset.assert_called_once_with(123)
    
    @patch('ctypes.WinDLL')
    def test_cnc_start(self, mock_win_dll):
        """測試 CNC 啟動"""
        mock_win_dll.return_value = self.mock_dll
        self.mock_dll.cnc_start.return_value = Status.EW_OK
        
        cnc = MachsyncFocas(self.dll_path)
        ret = cnc.CNC_Start(123)
        
        self.assertEqual(ret, Status.EW_OK)
        self.mock_dll.cnc_start.assert_called_once_with(123)


class TestDataStructures(unittest.TestCase):
    """測試數據結構"""
    
    def test_point_structure(self):
        """測試 Point 結構"""
        point = Point()
        point.x = 100
        point.y = 200
        point.z = 300
        
        self.assertEqual(point.x, 100)
        self.assertEqual(point.y, 200)
        self.assertEqual(point.z, 300)
    
    def test_dynamic_data_structure(self):
        """測試 DynamicData 結構"""
        dynamic_data = DynamicData()
        dynamic_data.axis = 3
        dynamic_data.alarm = 0
        dynamic_data.prgnum = 1000
        dynamic_data.prgmnum = 1000
        dynamic_data.seqnum = 100
        dynamic_data.actf = 500
        dynamic_data.acts = 1000
        
        self.assertEqual(dynamic_data.axis, 3)
        self.assertEqual(dynamic_data.alarm, 0)
        self.assertEqual(dynamic_data.prgnum, 1000)
    
    def test_faxis_structure(self):
        """測試 FAXIS 結構"""
        faxis = FAXIS()
        
        # 設置絕對位置
        faxis.absolute[0] = 100
        faxis.absolute[1] = 200
        faxis.absolute[2] = 300
        
        self.assertEqual(faxis.absolute[0], 100)
        self.assertEqual(faxis.absolute[1], 200)
        self.assertEqual(faxis.absolute[2], 300)
    
    def test_odbdy2_1_structure(self):
        """測試 ODBDY2_1 結構"""
        odbdy2_1 = ODBDY2_1()
        odbdy2_1.axis = 3
        odbdy2_1.alarm = 0
        odbdy2_1.prgnum = 1000
        
        self.assertEqual(odbdy2_1.axis, 3)
        self.assertEqual(odbdy2_1.alarm, 0)
        self.assertEqual(odbdy2_1.prgnum, 1000)


class TestStatusEnum(unittest.TestCase):
    """測試 Status 枚舉"""
    
    def test_status_values(self):
        """測試狀態值"""
        self.assertEqual(Status.EW_OK.value, 0)
        self.assertEqual(Status.EW_BUSY.value, -1)
        self.assertEqual(Status.EW_RESET.value, -2)
        self.assertEqual(Status.EW_SYSTEM.value, -5)
    
    def test_status_names(self):
        """測試狀態名稱"""
        self.assertEqual(Status.EW_OK.name, "EW_OK")
        self.assertEqual(Status.EW_BUSY.name, "EW_BUSY")
        self.assertEqual(Status.EW_RESET.name, "EW_RESET")


class TestIntegration(unittest.TestCase):
    """整合測試"""
    
    @patch('ctypes.WinDLL')
    def test_full_workflow(self, mock_win_dll):
        """測試完整的工作流程"""
        mock_win_dll.return_value = Mock()
        mock_dll = mock_win_dll.return_value
        
        # 設置所有函數調用都返回成功
        mock_dll.cnc_allclibhndl3.return_value = Status.EW_OK
        mock_dll.cnc_rddynamic2.return_value = Status.EW_OK
        mock_dll.cnc_exaxisname.return_value = Status.EW_OK
        mock_dll.cnc_rdgcode.return_value = Status.EW_OK
        mock_dll.cnc_rdspeed.return_value = Status.EW_OK
        mock_dll.cnc_rdposition.return_value = Status.EW_OK
        mock_dll.cnc_sysinfo.return_value = Status.EW_OK
        mock_dll.cnc_rdcommand.return_value = Status.EW_OK
        mock_dll.cnc_gettimer.return_value = Status.EW_OK
        mock_dll.cnc_rdtofsr.return_value = Status.EW_OK
        mock_dll.cnc_rdtofsinfo.return_value = Status.EW_OK
        mock_dll.cnc_freelibhndl.return_value = Status.EW_OK
        
        # 創建臨時 DLL 文件
        temp_dir = tempfile.mkdtemp()
        dll_path = os.path.join(temp_dir, "Fwlib64.dll")
        with open(dll_path, 'w') as f:
            f.write("fake dll content")
        
        try:
            cnc = MachsyncFocas(dll_path)
            
            # 連接
            ret, handle = cnc.CNC_Connect("192.168.1.100")
            self.assertEqual(ret, Status.EW_OK)
            
            # 讀取各種數據
            status, dynamic_data = cnc.CNC_ReadDynamicData(handle)
            self.assertEqual(status, Status.EW_OK)
            
            status, axis_num, axis_names = cnc.CNC_ReadAxisNames(handle)
            self.assertEqual(status, Status.EW_OK)
            
            status, num_gcd, gcode_list = cnc.CNC_ReadGCode(handle)
            self.assertEqual(status, Status.EW_OK)
            
            status, speed_data = cnc.CNC_ReadSpeedData(handle)
            self.assertEqual(status, Status.EW_OK)
            
            status, data_num, position_data = cnc.CNC_ReadPositionData(handle)
            self.assertEqual(status, Status.EW_OK)
            
            status, system_info = cnc.CNC_ReadSystemInfo(handle)
            self.assertEqual(status, Status.EW_OK)
            
            # 斷開連接
            ret = cnc.CNC_Disconnect(handle)
            self.assertEqual(ret, Status.EW_OK)
            
        finally:
            shutil.rmtree(temp_dir)


if __name__ == '__main__':
    # 創建測試套件
    test_suite = unittest.TestSuite()
    
    # 添加所有測試類別
    test_classes = [
        TestMachsyncFocas,
        TestDataStructures,
        TestStatusEnum,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 運行測試
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 輸出測試結果摘要
    print(f"\n{'='*50}")
    print(f"測試結果摘要:")
    print(f"運行測試數量: {result.testsRun}")
    print(f"失敗數量: {len(result.failures)}")
    print(f"錯誤數量: {len(result.errors)}")
    print(f"跳過數量: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print(f"\n失敗的測試:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\n錯誤的測試:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print(f"{'='*50}") 