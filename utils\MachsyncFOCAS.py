# -*- coding: utf-8 -*-
import os
import ctypes
import platform
from ctypes import byref, c_short, c_ushort, c_long, c_char_p, c_longlong, Structure, POINTER, c_int

from enum import Enum

# 定義常數
MAX_AXIS = 8  # 最大軸數

# 定義 Point 結構（對應 C# 的 Point 結構）
class Point(Structure):
    _fields_ = [
        ("x", c_longlong),  # X axis position
        ("y", c_longlong),  # Y axis position
        ("z", c_longlong),  # Z axis position
    ]

# 定義 DynamicData 結構（對應 C# 的 DynamicData 結構）
class DynamicData(Structure):
    _fields_ = [
        ("axis", c_short),      # axis number
        ("alarm", c_longlong),  # alarm status
        ("prgnum", c_longlong), # current program number
        ("prgmnum", c_longlong), # main program number
        ("seqnum", c_longlong), # current sequence number
        ("actf", c_longlong),   # actual feedrate
        ("acts", c_longlong),   # actual spindle speed
        ("absolutePos", Point), # absolute position
        ("relativePos", Point), # relative position
        ("machinePos", Point),  # machine position
    ]

# 定義 FAXIS 結構（對應 C# 的 FAXIS 結構）
class FAXIS(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("absolute", c_int * MAX_AXIS),    # absolute position
        ("machine", c_int * MAX_AXIS),     # machine position
        ("relative", c_int * MAX_AXIS),    # relative position
        ("distance", c_int * MAX_AXIS),    # distance to go
    ]

# 定義 FANUC ODBDY2_1 結構（用於 cnc_rddynamic2 函數）
class ODBDY2_1(Structure):
    _fields_ = [
        ("dummy", c_short),     # not used
        ("axis", c_short),      # axis number
        ("alarm", c_longlong),  # alarm status
        ("prgnum", c_longlong), # current program number
        ("prgmnum", c_longlong), # main program number
        ("seqnum", c_longlong), # current sequence number
        ("actf", c_longlong),   # actual feedrate
        ("acts", c_longlong),   # actual spindle speed
        ("pos", FAXIS),         # position data (absolute, relative, machine, distance)
    ]

# 定義 ODBEXAXISNAME 結構（用於存儲軸名稱）
class ODBEXAXISNAME(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("axname1", ctypes.c_char * 4),   # 軸名稱1
        ("axname2", ctypes.c_char * 4),   # 軸名稱2
        ("axname3", ctypes.c_char * 4),   # 軸名稱3
        ("axname4", ctypes.c_char * 4),   # 軸名稱4
        ("axname5", ctypes.c_char * 4),   # 軸名稱5
        ("axname6", ctypes.c_char * 4),   # 軸名稱6
        ("axname7", ctypes.c_char * 4),   # 軸名稱7
        ("axname8", ctypes.c_char * 4),   # 軸名稱8
        ("axname9", ctypes.c_char * 4),   # 軸名稱9
        ("axname10", ctypes.c_char * 4),  # 軸名稱10
        ("axname11", ctypes.c_char * 4),  # 軸名稱11
        ("axname12", ctypes.c_char * 4),  # 軸名稱12
        ("axname13", ctypes.c_char * 4),  # 軸名稱13
        ("axname14", ctypes.c_char * 4),  # 軸名稱14
        ("axname15", ctypes.c_char * 4),  # 軸名稱15
        ("axname16", ctypes.c_char * 4),  # 軸名稱16
        ("axname17", ctypes.c_char * 4),  # 軸名稱17
        ("axname18", ctypes.c_char * 4),  # 軸名稱18
        ("axname19", ctypes.c_char * 4),  # 軸名稱19
        ("axname20", ctypes.c_char * 4),  # 軸名稱20
        ("axname21", ctypes.c_char * 4),  # 軸名稱21
        ("axname22", ctypes.c_char * 4),  # 軸名稱22
        ("axname23", ctypes.c_char * 4),  # 軸名稱23
        ("axname24", ctypes.c_char * 4),  # 軸名稱24
        ("axname25", ctypes.c_char * 4),  # 軸名稱25
        ("axname26", ctypes.c_char * 4),  # 軸名稱26
        ("axname27", ctypes.c_char * 4),  # 軸名稱27
        ("axname28", ctypes.c_char * 4),  # 軸名稱28
        ("axname29", ctypes.c_char * 4),  # 軸名稱29
        ("axname30", ctypes.c_char * 4),  # 軸名稱30
        ("axname31", ctypes.c_char * 4),  # 軸名稱31
        ("axname32", ctypes.c_char * 4),  # 軸名稱32
    ]

# 定義 ODBGCD_data 結構（對應 C# 的 ODBGCD_data 結構）
class ODBGCD_data(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("group", c_short),                    # group
        ("flag", c_short),                     # flag
        ("code", ctypes.c_char * 8),          # code (固定長度8字元)
    ]

# 定義 ODBGCD 結構（對應 C# 的 ODBGCD 結構）
class ODBGCD(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("gcd0", ODBGCD_data),    # gcd0
        ("gcd1", ODBGCD_data),    # gcd1
        ("gcd2", ODBGCD_data),    # gcd2
        ("gcd3", ODBGCD_data),    # gcd3
        ("gcd4", ODBGCD_data),    # gcd4
        ("gcd5", ODBGCD_data),    # gcd5
        ("gcd6", ODBGCD_data),    # gcd6
        ("gcd7", ODBGCD_data),    # gcd7
        ("gcd8", ODBGCD_data),    # gcd8
        ("gcd9", ODBGCD_data),    # gcd9
        ("gcd10", ODBGCD_data),   # gcd10
        ("gcd11", ODBGCD_data),   # gcd11
        ("gcd12", ODBGCD_data),   # gcd12
        ("gcd13", ODBGCD_data),   # gcd13
        ("gcd14", ODBGCD_data),   # gcd14
        ("gcd15", ODBGCD_data),   # gcd15
        ("gcd16", ODBGCD_data),   # gcd16
        ("gcd17", ODBGCD_data),   # gcd17
        ("gcd18", ODBGCD_data),   # gcd18
        ("gcd19", ODBGCD_data),   # gcd19
        ("gcd20", ODBGCD_data),   # gcd20
        ("gcd21", ODBGCD_data),   # gcd21
        ("gcd22", ODBGCD_data),   # gcd22
        ("gcd23", ODBGCD_data),   # gcd23
        ("gcd24", ODBGCD_data),   # gcd24
        ("gcd25", ODBGCD_data),   # gcd25
        ("gcd26", ODBGCD_data),   # gcd26
        ("gcd27", ODBGCD_data),   # gcd27
    ]

# 定義 SPEEDELM 結構（對應 C# 的 SPEEDELM 結構）
class SPEEDELM(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("data", c_int),      # speed data
        ("dec", c_short),     # decimal position
        ("unit", c_short),    # data unit
        ("disp", c_short),    # display flag
        ("name", ctypes.c_ubyte),  # name of data
        ("suff", ctypes.c_ubyte),  # suffix
    ]

# 定義 ODBSPEED 結構（對應 C# 的 ODBSPEED 結構）
class ODBSPEED(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("actf", SPEEDELM),   # actual feed rate
        ("acts", SPEEDELM),   # actual spindle speed
    ]

# 定義 POSELM 結構（對應 C# 的 POSELM 結構）
class POSELM(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("data", c_int),      # position data
        ("dec", c_short),     # place of decimal point of position data
        ("unit", c_short),    # unit of position data
        ("disp", c_short),    # status of display
        ("name", ctypes.c_ubyte),  # axis name
        ("suff", ctypes.c_ubyte),  # axis name prefix
    ]

# 定義 POSELMALL 結構（對應 C# 的 POSELMALL 結構）
class POSELMALL(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("abs", POSELM),      # absolute position
        ("mach", POSELM),     # machine position
        ("rel", POSELM),      # relative position
        ("dist", POSELM),     # distance to go
    ]

# 定義 ODBPOS 結構（對應 C# 的 ODBPOS 結構）
class ODBPOS(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("p1", POSELMALL),    # position data for axis 1
        ("p2", POSELMALL),    # position data for axis 2
        ("p3", POSELMALL),    # position data for axis 3
        ("p4", POSELMALL),    # position data for axis 4
        ("p5", POSELMALL),    # position data for axis 5
        ("p6", POSELMALL),    # position data for axis 6
        ("p7", POSELMALL),    # position data for axis 7
        ("p8", POSELMALL),    # position data for axis 8
        # In case of 8 axes.
        # if you need the more information, you must be add the member.
    ]

# 定義 ODBCMD_data 結構（對應 C# 的 ODBCMD_data 結構）
class ODBCMD_data(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("adrs", ctypes.c_ubyte),    # address
        ("num", ctypes.c_ubyte),     # number
        ("flag", c_short),           # flag
        ("cmd_val", c_int),          # command value
        ("dec_val", c_int),          # decimal value
    ]

# 定義 ODBCMD 結構（對應 C# 的 ODBCMD 結構）
class ODBCMD(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("cmd0", ODBCMD_data),    # cmd0
        ("cmd1", ODBCMD_data),    # cmd1
        ("cmd2", ODBCMD_data),    # cmd2
        ("cmd3", ODBCMD_data),    # cmd3
        ("cmd4", ODBCMD_data),    # cmd4
        ("cmd5", ODBCMD_data),    # cmd5
        ("cmd6", ODBCMD_data),    # cmd6
        ("cmd7", ODBCMD_data),    # cmd7
        ("cmd8", ODBCMD_data),    # cmd8
        ("cmd9", ODBCMD_data),    # cmd9
        ("cmd10", ODBCMD_data),   # cmd10
        ("cmd11", ODBCMD_data),   # cmd11
        ("cmd12", ODBCMD_data),   # cmd12
        ("cmd13", ODBCMD_data),   # cmd13
        ("cmd14", ODBCMD_data),   # cmd14
        ("cmd15", ODBCMD_data),   # cmd15
        ("cmd16", ODBCMD_data),   # cmd16
        ("cmd17", ODBCMD_data),   # cmd17
        ("cmd18", ODBCMD_data),   # cmd18
        ("cmd19", ODBCMD_data),   # cmd19
        ("cmd20", ODBCMD_data),   # cmd20
        ("cmd21", ODBCMD_data),   # cmd21
        ("cmd22", ODBCMD_data),   # cmd22
        ("cmd23", ODBCMD_data),   # cmd23
        ("cmd24", ODBCMD_data),   # cmd24
        ("cmd25", ODBCMD_data),   # cmd25
        ("cmd26", ODBCMD_data),   # cmd26
        ("cmd27", ODBCMD_data),   # cmd27
        ("cmd28", ODBCMD_data),   # cmd28
        ("cmd29", ODBCMD_data),   # cmd29
    ]

# 定義 TIMER_DATE 結構（對應 C# 的 TIMER_DATE 結構）
class TIMER_DATE(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("year", c_short),    # year
        ("month", c_short),   # month
        ("date", c_short),    # date
    ]

# 定義 TIMER_TIME 結構（對應 C# 的 TIMER_TIME 結構）
class TIMER_TIME(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("hour", c_short),    # hour
        ("minute", c_short),  # minute
        ("second", c_short),  # second
    ]

# 定義 IODBTIMER 結構（對應 C# 的 IODBTIMER 結構）
# 注意：C#的[StructLayout(LayoutKind.Explicit)]在Python中需要使用Union來實現
class IODBTIMER(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("type", c_short),        # type (offset 0)
        ("dummy", c_short),       # dummy (offset 2)
        ("date", TIMER_DATE),     # date (offset 4) - 與time共用同一記憶體位置
    ]

# 定義 IODBTIMER_TIME 結構（用於訪問time字段）
class IODBTIMER_TIME(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("type", c_short),        # type (offset 0)
        ("dummy", c_short),       # dummy (offset 2)
        ("time", TIMER_TIME),     # time (offset 4) - 與date共用同一記憶體位置
    ]

# 定義 OFS_3 結構（對應 C# 的 OFS_3 結構）
class OFS_3(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("m_ofs_c", c_int * 20),  # M-C All (4*5 = 20個整數)
        # 工具偏移數據陣列，每個工具有4個偏移值：
        # m_ofs_c[4*i+0]: Tool length/geometry (工具長度/幾何)
        # m_ofs_c[4*i+1]: Tool length/wear (工具長度/磨損)
        # m_ofs_c[4*i+2]: Cutter radius/geometry (刀具半徑/幾何)
        # m_ofs_c[4*i+3]: Cutter radius/wear (刀具半徑/磨損)
        # 其中 i 是工具編號 (0-4，共5個工具)
    ]

# 定義 IODBTO_1_3 結構（對應 C# 的 IODBTO_1_3 結構）
class IODBTO_1_3(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("datano_s", c_short),    # start offset number
        ("type", c_short),        # offset type
        ("datano_e", c_short),    # end offset number
        ("ofs", OFS_3),          # offset data
    ]

# 定義 ODBSYS 結構（對應 C# 的 ODBSYS 結構）
class ODBSYS(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("addinfo", c_short),                    # addinfo
        ("max_axis", c_short),                   # max_axis
        ("cnc_type", ctypes.c_char * 2),         # cnc_type (固定長度2字元)
        ("mt_type", ctypes.c_char * 2),          # mt_type (固定長度2字元)
        ("series", ctypes.c_char * 4),           # series (固定長度4字元)
        ("version", ctypes.c_char * 4),          # version (固定長度4字元)
        ("axes", ctypes.c_char * 2),             # axes (固定長度2字元)
    ]

# 定義 ODBTLINF 結構（對應 C# 的 ODBTLINF 結構）
class ODBTLINF(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("ofs_type", c_short),    # Memory type of tool offset
                                   # 0: memory type A
                                   # 1: memory type B  
                                   # 2: memory type C
        ("use_no", c_short),      # Available number of tool offset
    ]

# 定義 IODBTD 結構（對應 C# 的 IODBTD 結構）
class IODBTD(Structure):
    _pack_ = 4  # 對應 C# 的 Pack=4
    _fields_ = [
        ("datano", c_short),      # tool group number
        ("type", c_short),        # tool using number
        ("tool_num", c_int),      # tool number
        ("h_code", c_int),        # H code
        ("d_code", c_int),        # D code
        ("tool_inf", c_int),      # tool information
    ]

class Status(Enum):
    EW_PROTOCOL = -17        # protocol error
    EW_SOCKET = -16          # Windows socket error
    EW_NODLL = -15           # DLL not exist error
    EW_BUS = -11             # bus error
    EW_SYSTEM2 = -10         # system error
    EW_HSSB = -9             # hssb communication error
    EW_HANDLE = -8           # Windows library handle error
    EW_VERSION = -7          # CNC/PMC version mismatch
    EW_UNEXP = -6            # abnormal error
    EW_SYSTEM = -5           # system error
    EW_PARITY = -4           # shared RAM parity error
    EW_MMCSYS = -3           # emm386 or mmcsys install error
    EW_RESET = -2            # reset or stop occurred error
    EW_BUSY = -1             # busy error
    EW_OK = 0                # no problem

    EW_FUNC = 1              # command prepare error
    EW_NOPMC = 1             # pmc not exist (same value as EW_FUNC)
    EW_LENGTH = 2            # data block length error
    EW_NUMBER = 3            # data number error
    EW_RANGE = 3             # address range error (same value as EW_NUMBER)
    EW_ATTRIB = 4            # data attribute error
    EW_TYPE = 4              # data type error (same value as EW_ATTRIB)
    EW_DATA = 5              # data error
    EW_NOOPT = 6             # no option error
    EW_PROT = 7              # write protect error
    EW_OVRFLOW = 8           # memory overflow error
    EW_PARAM = 9             # cnc parameter not correct error
    EW_BUFFER = 10           # buffer error
    EW_PATH = 11             # path error
    EW_MODE = 12             # cnc mode error
    EW_REJECT = 13           # execution rejected error
    EW_DTSRVR = 14           # data server error
    EW_ALARM = 15            # alarm has been occurred
    EW_STOP = 16             # CNC is not running
    EW_PASSWD = 17           # protection data error

    # DNC operation result codes
    DNC_NORMAL = -1          # normal completed (same value as EW_BUSY)
    DNC_CANCEL = -32768      # DNC operation was canceled by CNC
    DNC_OPENERR = -514       # file open error
    DNC_NOFILE = -516        # file not found
    DNC_READERR = -517       # read error

class MachsyncFocas:
    def __init__(self, dllPath):
        self.dllPath = dllPath
        self.focas = None
        self._load_dll()
        self._setup_functions()
    
    def _load_dll(self):
        """載入 DLL"""
        if not os.path.exists(self.dllPath):
            raise FileNotFoundError(f"找不到 DLL 文件：{self.dllPath}")
        
        self.focas = ctypes.WinDLL(self.dllPath)
        print("✓ 成功載入 MachsyncFocas.dll")
    
    def _setup_functions(self):
        """設定函數簽名"""
        # CNC 連接函數
        self.focas.cnc_allclibhndl3.argtypes = [
            c_char_p,      # IP 地址
            ctypes.c_ushort,      # 端口
            ctypes.c_int,         # 超時
            ctypes.POINTER(ctypes.c_ushort)  # 句柄指針
        ]
        self.focas.cnc_allclibhndl3.restype = ctypes.c_short
        
        # 讀取工具偏移值函數
        self.focas.cnc_rdtofs.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # 軸號
            ctypes.c_short,       # 工具號
            ctypes.POINTER(ctypes.c_double)  # 偏移值
        ]
        self.focas.cnc_rdtofs.restype = ctypes.c_short
        
        # 讀取伺服負載函數
        self.focas.cnc_rdsvmeter.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # 軸號
            ctypes.POINTER(ctypes.c_double)  # 負載值
        ]
        self.focas.cnc_rdsvmeter.restype = ctypes.c_short
        
        # 讀取絕對座標函數
        self.focas.cnc_absolute.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # 軸號
            ctypes.c_short,       # 數據長度
            ctypes.POINTER(ctypes.c_double)  # 座標數據
        ]
        self.focas.cnc_absolute.restype = ctypes.c_short
        
        # 釋放句柄函數
        self.focas.cnc_freelibhndl.argtypes = [ctypes.c_ushort]
        self.focas.cnc_freelibhndl.restype = ctypes.c_short
        
        # 讀取動態數據函數
        self.focas.cnc_rddynamic2.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # 軸數
            ctypes.c_short,       # 數據長度
            POINTER(ODBDY2_1)     # 動態數據結構
        ]
        self.focas.cnc_rddynamic2.restype = ctypes.c_short
        
        # 讀取軸名稱函數
        self.focas.cnc_exaxisname.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # Specify the kind of axis name to be read. 0	: Controlled axis name. 1 : Spindle name.
            ctypes.POINTER(ctypes.c_short),  # 軸數指針
            ctypes.POINTER(ODBEXAXISNAME)    # 軸名稱結構指針
        ]
        self.focas.cnc_exaxisname.restype = ctypes.c_short
        
        # 讀取G代碼函數
        self.focas.cnc_rdgcode.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # Specify the group of G code.
            ctypes.c_short,       # Specify the block to be read.
            ctypes.POINTER(ctypes.c_short),  # G代碼數量指針
            ctypes.POINTER(ODBGCD)           # G代碼數據結構指針
        ]
        self.focas.cnc_rdgcode.restype = ctypes.c_short
        
        # 讀取速度數據函數
        self.focas.cnc_rdspeed.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # Specify the data type. 0 : feed rate. 1 : spindle speed. -1 : all
            ctypes.POINTER(ODBSPEED)  # 速度數據結構指針
        ]
        self.focas.cnc_rdspeed.restype = ctypes.c_short
        
        # 讀取位置數據函數
        self.focas.cnc_rdposition.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # Specify the kind of position data to be read. 0 : absolute position. 1 : machine position. 2 : relative position. 3 : distance to go. -1 : all
            ctypes.POINTER(ctypes.c_short),  # Specify the pointer to the number of data to be read. This function returns the number of data which was read actually.
            ctypes.POINTER(ODBPOS)  # 位置數據結構指針
        ]
        self.focas.cnc_rdposition.restype = ctypes.c_short
        
        # 讀取系統信息函數
        self.focas.cnc_sysinfo.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.POINTER(ODBSYS)  # 系統信息結構指針
        ]
        self.focas.cnc_sysinfo.restype = ctypes.c_short
        
        # 讀取命令數據函數
        self.focas.cnc_rdcommand.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # 指定要讀取的命令數據類型
            ctypes.c_short,       # 指定要讀取的塊。0: 前一塊, 1: 活動塊, 2: 下一塊
            ctypes.POINTER(ctypes.c_short),  # 數據數量指針
            ctypes.POINTER(ODBCMD)  # 命令數據結構指針
        ]
        self.focas.cnc_rdcommand.restype = ctypes.c_short
        
        # 讀取計時器數據函數
        self.focas.cnc_gettimer.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.POINTER(IODBTIMER)  # 計時器數據結構指針
        ]
        self.focas.cnc_gettimer.restype = ctypes.c_short
        
        # 讀取工具偏移數據函數 (cnc_rdtofsr)
        self.focas.cnc_rdtofsr.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # 開始偏移編號 (s_number)
            ctypes.c_short,       # 偏移類型 (type)
            ctypes.c_short,       # 結束偏移編號 (e_number)
            ctypes.c_short,       # 數據長度 (length)
            ctypes.POINTER(IODBTO_1_3)  # 工具偏移數據結構指針
        ]
        self.focas.cnc_rdtofsr.restype = ctypes.c_short
        
        # 讀取工具偏移信息函數 (cnc_rdtofsinfo)
        self.focas.cnc_rdtofsinfo.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.POINTER(ODBTLINF)  # 工具偏移信息結構指針
        ]
        self.focas.cnc_rdtofsinfo.restype = ctypes.c_short
        
        # 讀取單個工具壽命數據函數 (cnc_rd1tlifedata)
        self.focas.cnc_rd1tlifedata.argtypes = [
            ctypes.c_ushort,      # 句柄
            ctypes.c_short,       # 工具組號 (grp_no)
            ctypes.c_short,       # 工具號 (tool_no)
            ctypes.POINTER(IODBTD)  # 工具數據結構指針
        ]
        self.focas.cnc_rd1tlifedata.restype = ctypes.c_short
        
        print("✓ 函數簽名設定完成")
    
    def CNC_Connect(self, ip, port=8193, timeout=3):
        """連接到 CNC 機器"""
        handle = ctypes.c_ushort(0)
        ret = self.focas.cnc_allclibhndl3(
            ip.encode('utf-8'), 
            port, 
            timeout, 
            byref(handle)
        )
        
        return ret, handle.value
    
    def CNC_Disconnect(self, handle):
        """斷開連接"""
        ret = self.focas.cnc_freelibhndl(handle)
        return ret

    @staticmethod
    def GetStatusDescription(status_code):
        descriptions = {
            Status.EW_PROTOCOL: (
                "Protocol error (Ethernet version only)",
                "Data from Ethernet Board is incorrect."
            ),
            Status.EW_SOCKET: (
                "Socket error (Ethernet version only)",
                "Investigate CNC power supply, Ethernet cable and I/F board."
            ),
            Status.EW_NODLL: (
                "DLL file error",
                "There is no DLL file for each CNC series corresponding to specified node."
            ),
            Status.EW_BUS: (
                "Bus error (HSSB version only)",
                "A bus error of CNC system occurred."
            ),
            Status.EW_SYSTEM2: (
                "System error (2) (HSSB version only)",
                "A system error of CNC system occurred."
            ),
            Status.EW_HSSB: (
                "Communication error of HSSB (HSSB version only)",
                "Investigate the serial line or I/F board of HSSB."
            ),
            Status.EW_HANDLE: (
                "Handle number error",
                "Get the library handle number."
            ),
            Status.EW_VERSION: (
                "Version mismatch between the CNC/PMC and library",
                "The CNC/PMC version does not match that of the library. Replace the library or the CNC/PMC control software."
            ),
            Status.EW_UNEXP: (
                "Abnormal library state",
                "An unanticipated error occurred."
            ),
            Status.EW_SYSTEM: (
                "System error (HSSB version only)",
                "A system error of CNC occurred."
            ),
            Status.EW_PARITY: (
                "Shared RAM parity error (HSSB version only)",
                "A hardware error occurred."
            ),
            Status.EW_MMCSYS: (
                "FANUC drivers installation error (HSSB version only)",
                "The drivers required for execution are not installed."
            ),
            Status.EW_RESET: (
                "Reset or stop request",
                "The RESET or STOP button was pressed."
            ),
            Status.EW_BUSY: (
                "Busy",
                "Wait until the completion of CNC processing, or retry."
            ),
            Status.EW_OK: (
                "Normal termination",
                ""
            ),
            Status.EW_FUNC: (
                "Error(function is not executed, or not available)",
                "Specific function which must be executed beforehand has not been executed. Otherwise that function is not available."
            ),
            Status.EW_LENGTH: (
                "Error(data block length error, error of number of data)",
                "Check and correct the data block length or number of data."
            ),
            Status.EW_NUMBER: (
                "Error(data number error)",
                "Check and correct the data number."
            ),
            Status.EW_ATTRIB: (
                "Error(data attribute error)",
                "Check and correct the data attribute."
            ),
            Status.EW_DATA: (
                "Error(data error)",
                "Check and correct the data. For the following operations, this code indicates that the specified program cannot be found.\nDelete specified program\nSearch specified program\nStart uploading NC program"
            ),
            Status.EW_NOOPT: (
                "Error(no option)",
                "There is no corresponding CNC option."
            ),
            Status.EW_PROT: (
                "Error(write protection)",
                "Write operation is prohibited."
            ),
            Status.EW_OVRFLOW: (
                "Error(memory overflow)",
                "CNC tape memory is overflowed."
            ),
            Status.EW_PARAM: (
                "Error(CNC parameter error)",
                "CNC parameter is set incorrectly."
            ),
            Status.EW_BUFFER: (
                "Error(buffer empty/full)",
                "The buffer is empty or full. Wait until completion of CNC processing, or retry."
            ),
            Status.EW_PATH: (
                "Error(path number error)",
                "A path number is incorrect."
            ),
            Status.EW_MODE: (
                "Error(CNC mode error)",
                "The CNC mode is incorrect. Correct the CNC mode."
            ),
            Status.EW_REJECT: (
                "Error(CNC execution rejection)",
                "The execution at the CNC is rejected. Check the condition of execution."
            ),
            Status.EW_DTSRVR: (
                "Error(Data server error)",
                "Some errors occur at the data server."
            ),
            Status.EW_ALARM: (
                "Error(alarm)",
                "The function cannot be executed due to an alarm in CNC. Remove the cause of alarm."
            ),
            Status.EW_STOP: (
                "Error(stop)",
                "CNC status is stop or emergency."
            ),
            Status.EW_PASSWD: (
                "Error(State of data protection)",
                "Data is protected by the CNC data protection function."
            ),
        }

        if status_code in descriptions:
            meaning, explanation = descriptions[status_code]
            return f"{status_code.name} - \n    meaning: {meaning}\n    explanation: {explanation}"
        else:
            return f"No such status code: {status_code}"

    def GetAlarmDescription(self, alarm_value):
        """解析32位元警報狀態
        
        Args:
            alarm_value (int): 32位元整數，包含警報狀態位元
            
        Returns:
            dict: 包含所有警報狀態的字典，鍵為位元編號，值為 (警報類型, 描述, 是否啟用)
        """
        alarm_definitions = {
            0: ("SW", "Parameter switch on"),
            1: ("PW", "Power off parameter set"),
            2: ("IO", "I/O error"),
            3: ("PS", "Foreground P/S"),
            4: ("OT", "Overtravel,External data"),
            5: ("OH", "Overheat alarm"),
            6: ("SV", "Servo alarm"),
            7: ("SR", "Data I/O error"),
            8: ("MC", "Macro alarm"),
            9: ("SP", "Spindle alarm"),
            10: ("DS", "Other alarm(DS)"),
            11: ("IE", "Alarm concerning Malfunction prevent functions"),
            12: ("BG", "Background P/S"),
            13: ("SN", "Syncronized error"),
            14: ("", "(reserved)"),
            15: ("EX", "External alarm message"),
            16: ("", "(reserved)"),
            17: ("", "(reserved)"),
            18: ("", "(reserved)"),
            19: ("PC", "PMC error"),
            # 20-31: (Not used)
        }
        
        # 初始化結果字典
        alarm_status = {}
        
        # 檢查每個位元
        for bit in range(32):
            is_set = bool(alarm_value & (1 << bit))
            
            if bit in alarm_definitions:
                alarm_type, description = alarm_definitions[bit]
                alarm_status[bit] = (alarm_type, description, is_set)
            else:
                # 20-31位元未使用
                alarm_status[bit] = ("", "(Not used)", is_set)
        
        return alarm_status
    
    def CNC_Stop(self, handle):
        """Executes the external reset of CNC."""
        ret = self.focas.cnc_reset(handle)
        return ret

    def CNC_Start(self, handle):
        """cycle start."""
        ret = self.focas.cnc_start(handle)
        return ret

    def CNC_ReadDynamicData(self, libHandle):
        """讀取 CNC 動態數據（對應 C# 的 CNC_ReadDynamicData 方法）
        
        Returns:
            tuple: (status_code, dynamic_data)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - dynamic_data: DynamicData對象（成功時）或None（失敗時）
        """
        
        # 創建 ODBDY2_1 結構實例
        dynData = ODBDY2_1()
        
        # 調用 cnc_rddynamic2 函數
        ret = self.focas.cnc_rddynamic2(libHandle, -1, 28 + 4 * 4 * MAX_AXIS, byref(dynData))
        
        if ret != 0:
            return ret, None  # 返回錯誤碼和None
        
        # 創建 DynamicData 結構並填充數據
        dynamicData = DynamicData()
        
        # 填充基本數據
        dynamicData.axis = dynData.axis
        dynamicData.alarm = dynData.alarm
        dynamicData.prgnum = dynData.prgnum
        dynamicData.prgmnum = dynData.prgmnum
        dynamicData.seqnum = dynData.seqnum
        dynamicData.actf = dynData.actf
        dynamicData.acts = dynData.acts
        
        # 填充絕對位置數據（從 FAXIS 結構的 absolute 陣列）
        dynamicData.absolutePos.x = dynData.pos.absolute[0] if len(dynData.pos.absolute) > 0 else 0
        dynamicData.absolutePos.y = dynData.pos.absolute[1] if len(dynData.pos.absolute) > 1 else 0
        dynamicData.absolutePos.z = dynData.pos.absolute[2] if len(dynData.pos.absolute) > 2 else 0
        
        # 填充相對位置數據（從 FAXIS 結構的 relative 陣列）
        dynamicData.relativePos.x = dynData.pos.relative[0] if len(dynData.pos.relative) > 0 else 0
        dynamicData.relativePos.y = dynData.pos.relative[1] if len(dynData.pos.relative) > 1 else 0
        dynamicData.relativePos.z = dynData.pos.relative[2] if len(dynData.pos.relative) > 2 else 0
        
        # 填充機器位置數據（從 FAXIS 結構的 machine 陣列）
        dynamicData.machinePos.x = dynData.pos.machine[0] if len(dynData.pos.machine) > 0 else 0
        dynamicData.machinePos.y = dynData.pos.machine[1] if len(dynData.pos.machine) > 1 else 0
        dynamicData.machinePos.z = dynData.pos.machine[2] if len(dynData.pos.machine) > 2 else 0
        
        return Status.EW_OK, dynamicData

    def CNC_ReadAxisNames(self, handle):
        """讀取CNC軸名稱
        
        Args:
            handle: CNC句柄
            
        Returns:
            tuple: (status_code, axis_num, axis_names)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - axis_num: 實際軸數
                - axis_names: 軸名稱列表
        """
        axis_num = ctypes.c_short(32)
        axis_name = ODBEXAXISNAME()
        
        ret = self.focas.cnc_exaxisname(handle, 0, byref(axis_num), byref(axis_name))
        
        if ret == Status.EW_OK:
            # 提取軸名稱
            axis_names = []
            for i in range(1, 33):
                field_name = f"axname{i}"
                if hasattr(axis_name, field_name):
                    byte_data = getattr(axis_name, field_name)
                    if isinstance(byte_data, bytes):
                        name = byte_data.rstrip(b'\x00').decode('utf-8', errors='ignore').strip()
                    else:
                        name = str(byte_data).strip()
                    axis_names.append(name)
            
            return ret, axis_num.value, axis_names
        else:
            return ret, 0, []    
    
    def CNC_ReadGCode(self, handle):
        """讀取CNC G代碼數據
        
        Args:
            handle: CNC句柄
            
        Returns:
            tuple: (status_code, num_gcd, gcode_list)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - num_gcd: the number of G code data which was/were read actually is stored after the function call
                - gcode_list: 包含所有有效G代碼信息的列表
        """
        num_gcd = ctypes.c_short(28)  # 0-27，共28個
        gcode_data = ODBGCD()
        
        ret = self.focas.cnc_rdgcode(handle, -1, 1, byref(num_gcd), byref(gcode_data))
        
        if ret == Status.EW_OK:
            gcode_list = []
        
            for i in range(28):
                field_name = f"gcd{i}"
                if hasattr(gcode_data, field_name):
                    gcd_item = getattr(gcode_data, field_name)
                    
                    # 解析code字段
                    if isinstance(gcd_item.code, bytes):
                        code = gcd_item.code.rstrip(b'\x00').decode('utf-8', errors='ignore').strip()
                    else:
                        code = str(gcd_item.code).strip()
                    
                    # 只添加有效的G代碼（code不為空）
                    if code:
                        # 解析flag位元
                        flag_info = {
                            'bit3_command_diff': bool(gcd_item.flag & (1 << 3)),  # #3 = 1: command differ from the previous block
                            'bit7_has_command': bool(gcd_item.flag & (1 << 7)),   # #7 = 1: There is a command in the present block
                        }
                        
                        gcode_info = {
                            'index': i,
                            'group': gcd_item.group,
                            'flag': gcd_item.flag,  # 原始flag值
                            'flag_info': flag_info,
                            'code': code
                        }
                        gcode_list.append(gcode_info)

            return ret, num_gcd.value, gcode_list
        else:
            return ret, 0, None
    
    def CNC_ReadSpeedData(self, handle):
        """讀取CNC速度數據，主要是小數點位數跟單位，如果要持續監控位置跟速度，建議使用CNC_ReadDynamicData一次讀取所有數據
        
        Args:
            handle: CNC句柄
            
        Returns:
            tuple: (status_code, speed_data)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - speed_data: 包含實際進給速度和主軸速度的字典（成功時）或None（失敗時）
        """
        speed_data = ODBSPEED()
        
        # 讀取所有速度數據（進給速度和主軸速度）
        ret = self.focas.cnc_rdspeed(handle, -1, byref(speed_data))
        
        if ret == Status.EW_OK:
            unit_descriptions = {
                0: "mm/min",
                1: "inch/min", 
                2: "rpm",
                3: "mm/rev",
                4: "inch/rev"
            }

            # 解析速度數據
            speed_info = {
                'actual_feedrate': {
                    'data': speed_data.actf.data,
                    'decimal_position': speed_data.actf.dec,
                    'unit': unit_descriptions.get(speed_data.actf.unit, f"未知單位({speed_data.actf.unit})"),
                    'display_flag': speed_data.actf.disp,
                    'name': chr(speed_data.actf.name) if speed_data.actf.name > 0 else '',
                    'suffix': chr(speed_data.actf.suff) if speed_data.actf.suff > 0 else ''
                },
                'actual_spindle_speed': {
                    'data': speed_data.acts.data,
                    'decimal_position': speed_data.acts.dec,
                    'unit': unit_descriptions.get(speed_data.acts.unit, f"未知單位({speed_data.acts.unit})"),
                    'display_flag': speed_data.acts.disp,
                    'name': chr(speed_data.acts.name) if speed_data.acts.name > 0 else '',
                    'suffix': chr(speed_data.acts.suff) if speed_data.acts.suff > 0 else ''
                }
            }
            
            return ret, speed_info
        else:
            return ret, None
    
    def CNC_ReadPositionData(self, handle):
        """讀取CNC位置數據，包含小數點位數、單位和軸名稱，如果要持續監控位置跟速度，建議使用CNC_ReadDynamicData一次讀取所有數據
        
        Args:
            handle: CNC句柄
                
        Returns:
            tuple: (status_code, data_num, position_data)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - data_num: 實際讀取的軸數
                - position_data: 包含所有軸位置數據的字典（成功時）或None（失敗時）
        """
        position_data = ODBPOS()
        data_num = ctypes.c_short(8)  # 指定要讀取的軸數，8軸
        
        # 讀取位置數據
        ret = self.focas.cnc_rdposition(handle, -1, byref(data_num), byref(position_data))
        
        if ret == Status.EW_OK:
            unit_descriptions = {
                0: "mm",
                1: "inch", 
                2: "degree"
            }
            
            # 解析位置數據
            position_info = {}
            
            # 遍歷所有軸（p1-p8）
            for axis_idx in range(8):
                axis_key = f"axis_{axis_idx + 1}"
                axis_data = getattr(position_data, f"p{axis_idx + 1}")
                
                # 解析每種位置類型
                position_types = {
                    'absolute': axis_data.abs,
                    'machine': axis_data.mach,
                    'relative': axis_data.rel,
                    'distance': axis_data.dist
                }
                
                axis_info = {}
                for pos_type, pos_data in position_types.items():
                    # 解析軸名稱和後綴
                    axis_name = chr(pos_data.name) if pos_data.name > 0 else ''
                    axis_suffix = chr(pos_data.suff) if pos_data.suff > 0 else ''
                    
                    axis_info[pos_type] = {
                        'data': pos_data.data,
                        'decimal_position': pos_data.dec,
                        'unit': unit_descriptions.get(pos_data.unit, f"未知單位({pos_data.unit})"),
                        'display_status': pos_data.disp,
                        'axis_name': axis_name,
                        'axis_suffix': axis_suffix,
                        'full_axis_name': f"{axis_suffix}{axis_name}".strip()
                    }
                
                position_info[axis_key] = axis_info
            
            return ret, data_num.value, position_info
        else:
            return ret, 0, None
    
    def CNC_ReadSystemInfo(self, handle):
        """讀取CNC系統信息
        
        Args:
            handle: CNC句柄
            
        Returns:
            tuple: (status_code, system_info)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - system_info: 包含系統信息的字典（成功時）或None（失敗時）
        """
        sys_info = ODBSYS()
        
        ret = self.focas.cnc_sysinfo(handle, byref(sys_info))
        
        if ret == Status.EW_OK:
            # 解析 addinfo 字段
            addinfo_parsed = self._parse_addinfo(sys_info.addinfo)
            
            # 解析 cnc_type 字段
            cnc_type_raw = sys_info.cnc_type.rstrip(b'\x00').decode('utf-8', errors='ignore').strip()
            cnc_type_parsed = self._parse_cnc_type(cnc_type_raw)
            
            # 解析 mt_type 字段
            mt_type_raw = sys_info.mt_type.rstrip(b'\x00').decode('utf-8', errors='ignore').strip()
            mt_type_parsed = self._parse_mt_type(mt_type_raw)
            
            # 解析系統信息
            system_info = {
                'addinfo': sys_info.addinfo,
                'addinfo_parsed': addinfo_parsed,
                'max_axis': sys_info.max_axis,
                'cnc_type': cnc_type_raw,
                'cnc_type_parsed': cnc_type_parsed,
                'mt_type': mt_type_raw,
                'mt_type_parsed': mt_type_parsed,
                'series': sys_info.series.rstrip(b'\x00').decode('utf-8', errors='ignore').strip(),
                'version': sys_info.version.rstrip(b'\x00').decode('utf-8', errors='ignore').strip(),
                'axes': sys_info.axes.rstrip(b'\x00').decode('utf-8', errors='ignore').strip()
            }
            
            return ret, system_info
        else:
            return ret, None
    
    def _parse_addinfo(self, addinfo_value):
        """解析 addinfo 字段的位元信息
        
        Args:
            addinfo_value (int): addinfo 的原始值
            
        Returns:
            str: 解析後的 addinfo 描述字串
        """
        # 解析位元 0-7 的功能標誌
        loader_control = "with loader control function" if (addinfo_value & 1) else "without loader control function"
        i_series = "i Series CNC" if (addinfo_value & 2) else "not an i Series CNC"
        compound_machining = "with compound machining function" if (addinfo_value & 8) else "without compound machining function"
        transfer_line = "with transfer line function" if (addinfo_value & 16) else "without transfer line function"
        
        # 解析 MODEL 信息（位元 8-15）
        model_value = (addinfo_value >> 8) & 0xFF
        model_definitions = {
            0: 'MODEL information is not supported',
            1: 'MODEL A',
            2: 'MODEL B',
            3: 'MODEL C',
            4: 'MODEL D',
            6: 'MODEL F'
        }
        model_info = model_definitions.get(model_value, f'Unknown MODEL ({model_value})')
        
        # 組合描述字串
        description = f"{loader_control}, {i_series}, {compound_machining}, {transfer_line}, {model_info}"
        
        return description
    
    def _parse_cnc_type(self, cnc_type_value):
        """解析 cnc_type 字段的 CNC 型號信息
        
        Args:
            cnc_type_value (str): cnc_type 的原始字串值
            
        Returns:
            str: CNC 型號的字串描述
        """
        # CNC 型號定義
        cnc_type_definitions = {
            '15': 'Series 15/15i',
            '16': 'Series 16/16i',
            '18': 'Series 18/18i',
            '21': 'Series 21/21i',
            '30': 'Series 30i',
            '31': 'Series 31i',
            '32': 'Series 32i',
            '35': 'Series 35i',
            ' 0': 'Series 0i',  # 注意：這裡是空格+0，不是純0
            'PD': 'Power Mate i-D',
            'PH': 'Power Mate i-H',
            'PM': 'Power Motion i'
        }
        
        # 返回對應的型號字串，如果未知則返回原始值
        return cnc_type_definitions.get(cnc_type_value, f'Unknown ({cnc_type_value})')
    
    def _parse_mt_type(self, mt_type_value):
        """解析 mt_type 字段的機器類型信息
        
        Args:
            mt_type_value (str): mt_type 的原始字串值
            
        Returns:
            str: 機器類型的字串描述
        """
        # 機器類型定義
        mt_type_definitions = {
            ' M': 'Machining center',
            ' T': 'Lathe',
            'MM': 'M series with 2 path control',
            'TT': 'T series with 2/3 path control',
            'MT': 'T series with compound machining function',
            ' P': 'Punch press',
            ' L': 'Laser',
            ' W': 'Wire cut'
        }
        
        # 返回對應的機器類型字串，如果未知則返回原始值
        return mt_type_definitions.get(mt_type_value, f'Unknown ({mt_type_value})')
    
    def CNC_ReadModalDataExceptGCode(self, handle):
        """讀取CNC命令數據（對應C#的cnc_rdcommand函數）
        
        Args:
            handle: CNC句柄
            type: 指定要讀取的命令數據類型
                  0-29: 逐個讀取除G代碼外的模態數據
                  -1: 一次性讀取除G代碼外的所有模態數據
                  100-129: 逐個讀取命令數據
                  -2: 一次性讀取所有命令數據
                  200-223: 逐個讀取與軸相關的命令數據
                  -3: 一次性讀取所有與軸相關的命令數據
            block: 指定要讀取的塊
                  0: 前一塊
                  1: 活動塊
                  2: 下一塊
                
        Returns:
            tuple: (status_code, num_commands, command_list)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - num_commands: 實際讀取的命令數量
                - command_list: 包含所有有效命令信息的列表（成功時）或None（失敗時）
        """
        type = -1
        block = 1

        num = ctypes.c_short(30)  # 指定要讀取的數據數量
        command = ODBCMD()
        
        ret = self.focas.cnc_rdcommand(handle, type, block, byref(num), byref(command))
        
        if ret == Status.EW_OK:
            command_list = []
            
            # 遍歷所有命令（cmd0-cmd29）
            for i in range(30):
                field_name = f"cmd{i}"
                if hasattr(command, field_name):
                    cmd_item = getattr(command, field_name)
                    
                    # 只添加有效的命令（adrs不為0）
                    if cmd_item.adrs != 0:
                        # 將ASCII碼轉換為字元
                        address_char = chr(cmd_item.adrs)
                        
                        # 解析flag位元
                        flag_info = {
                            'bit3_command_diff': bool(cmd_item.flag & (1 << 3)),    # #3 = 1: command differ from the previous block (only Series 30i, 0i-D/F, PMi-A)
                            'bit4_has_decimal': bool(cmd_item.flag & (1 << 4)),     # #4 = 1: There is a command of a decimal point
                            'bit5_negative': bool(cmd_item.flag & (1 << 5)),        # #5 = 1: Negative
                            'bit6_one_digit': bool(cmd_item.flag & (1 << 6)),       # #6 = 1: 1 digit
                            'bit11_after_reset': bool(cmd_item.flag & (1 << 11)),   # #11 = 1: There is a command after RESET
                            'bit15_in_present_block': bool(cmd_item.flag & (1 << 15))  # #15 = 1: There is a command in the present block
                        }
                        
                        command_info = {
                            'index': i,
                            'address': address_char,  # 轉換後的字元
                            'number': cmd_item.num,
                            'flag': cmd_item.flag,  # 原始flag值
                            'flag_info': flag_info,  # 解析後的flag信息
                            'command_value': cmd_item.cmd_val,
                            'decimal_value': cmd_item.dec_val
                        }
                        command_list.append(command_info)
            
            return ret, num.value, command_list
        else:
            return ret, 0, None
    
    def CNC_ReadCommandedData(self, handle):
        """讀取CNC所有命令數據（對應C#的cnc_rdcommand函數，type=-2）
        
        Args:
            handle: CNC句柄
                
        Returns:
            tuple: (status_code, num_commands, command_list)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - num_commands: 實際讀取的命令數量
                - command_list: 包含所有有效命令信息的列表（成功時）或None（失敗時）
        """
        type = -2
        block = 1

        num = ctypes.c_short(30)  # 指定要讀取的數據數量
        command = ODBCMD()
        
        ret = self.focas.cnc_rdcommand(handle, type, block, byref(num), byref(command))
        
        if ret == Status.EW_OK:
            command_list = []
            
            # 遍歷所有命令（cmd0-cmd29）
            for i in range(30):
                field_name = f"cmd{i}"
                if hasattr(command, field_name):
                    cmd_item = getattr(command, field_name)
                    
                    # 只添加有效的命令（adrs不為0）
                    if cmd_item.adrs != 0:
                        # 將ASCII碼轉換為字元
                        address_char = chr(cmd_item.adrs)
                        
                        # 解析flag位元
                        flag_info = {
                            'bit4_has_decimal': bool(cmd_item.flag & (1 << 4)),     # #4 = 1: There is a command of a decimal point
                            'bit5_negative': bool(cmd_item.flag & (1 << 5)),        # #5 = 1: Negative
                            'bit6_one_digit': bool(cmd_item.flag & (1 << 6)),       # #6 = 1: 1 digit
                            'bit11_after_reset': bool(cmd_item.flag & (1 << 11)),   # #11 = 1: There is a command after RESET
                            'bit15_in_present_block': bool(cmd_item.flag & (1 << 15))  # #15 = 1: There is a command in the present block
                        }
                        
                        command_info = {
                            'index': i,
                            'address': address_char,  # 轉換後的字元
                            'number': cmd_item.num,
                            'flag': cmd_item.flag,  # 原始flag值
                            'flag_info': flag_info,  # 解析後的flag信息
                            'command_value': cmd_item.cmd_val,
                            'decimal_value': cmd_item.dec_val
                        }
                        command_list.append(command_info)
            
            return ret, num.value, command_list
        else:
            return ret, 0, None
    
    def CNC_ReadCommandedDataConcerningAxis(self, handle):
        """讀取CNC與軸相關的所有命令數據（對應C#的cnc_rdcommand函數，type=-3）
        
        Args:
            handle: CNC句柄
                
        Returns:
            tuple: (status_code, num_commands, command_list)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - num_commands: 實際讀取的命令數量
                - command_list: 包含所有有效命令信息的列表（成功時）或None（失敗時）
        """
        type = -3
        block = 1

        num = ctypes.c_short(30)  # 指定要讀取的數據數量
        command = ODBCMD()
        
        ret = self.focas.cnc_rdcommand(handle, type, block, byref(num), byref(command))
        
        if ret == Status.EW_OK:
            command_list = []
            
            # 遍歷所有命令（cmd0-cmd29）
            for i in range(30):
                field_name = f"cmd{i}"
                if hasattr(command, field_name):
                    cmd_item = getattr(command, field_name)
                    
                    # 只添加有效的命令（adrs不為0）
                    if cmd_item.adrs != 0:
                        # 將ASCII碼轉換為字元
                        address_char = chr(cmd_item.adrs)
                        
                        # 解析flag位元
                        flag_info = {
                            'bit4_has_decimal': bool(cmd_item.flag & (1 << 4)),     # #4 = 1: There is a command of a decimal point
                            'bit5_negative': bool(cmd_item.flag & (1 << 5)),        # #5 = 1: Negative
                            'bit6_one_digit': bool(cmd_item.flag & (1 << 6)),       # #6 = 1: 1 digit
                            'bit11_after_reset': bool(cmd_item.flag & (1 << 11)),   # #11 = 1: There is a command after RESET
                            'bit15_in_present_block': bool(cmd_item.flag & (1 << 15))  # #15 = 1: There is a command in the present block
                        }
                        
                        command_info = {
                            'index': i,
                            'address': address_char,  # 轉換後的字元
                            'number': cmd_item.num,
                            'flag': cmd_item.flag,  # 原始flag值
                            'flag_info': flag_info,  # 解析後的flag信息
                            'command_value': cmd_item.cmd_val,
                            'decimal_value': cmd_item.dec_val
                        }
                        command_list.append(command_info)
            
            return ret, num.value, command_list
        else:
            return ret, 0, None
    
    def CNC_ReadTimerData(self, handle, timer_type=0):
        """讀取CNC計時器數據（對應C#的cnc_gettimer函數）
        
        Args:
            handle: CNC句柄
            timer_type: 計時器類型
                       0: 獲取日期 (Gets date)
                       1: 獲取時間 (Gets time)
                
        Returns:
            tuple: (status_code, timer_data)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - timer_data: 包含日期或時間數據的字典（成功時）或None（失敗時）
        """
        # 創建 IODBTIMER 結構實例
        timer = IODBTIMER()
        timer.type = timer_type  # 0: Gets date, 1: Gets time
        
        # 調用 cnc_gettimer 函數
        ret = self.focas.cnc_gettimer(handle, byref(timer))
        
        if ret == Status.EW_OK:
            if timer_type == 0:
                # 讀取日期數據
                timer_data = {
                    'type': timer.type,
                    'dummy': timer.dummy,
                    'date': {
                        'year': timer.date.year,
                        'month': timer.date.month,
                        'date': timer.date.date
                    }
                }
            else:
                # 讀取時間數據
                # 注意：由於 IODBTIMER 結構中 date 和 time 共用同一記憶體位置
                # 我們需要創建一個 IODBTIMER_TIME 結構來訪問 time 字段
                timer_time = IODBTIMER_TIME()
                timer_time.type = timer.type
                timer_time.dummy = timer.dummy
                
                # 將 date 數據複製到 time 結構中
                import ctypes
                ctypes.memmove(byref(timer_time.time), byref(timer.date), ctypes.sizeof(TIMER_TIME))
                
                timer_data = {
                    'type': timer_time.type,
                    'dummy': timer_time.dummy,
                    'time': {
                        'hour': timer_time.time.hour,
                        'minute': timer_time.time.minute,
                        'second': timer_time.time.second
                    }
                }
            
            return ret, timer_data
        else:
            return ret, None

    def CNC_ReadToolOffsetData(self, handle, s_number=1, e_number=5):
        """讀取工具偏移數據（對應C#的cnc_rdtofsr函數）
        
        Args:
            handle: CNC句柄
            s_number: 開始偏移編號 (預設值: 1)
            type: 偏移類型 (預設值: -3)
            e_number: 結束偏移編號 (預設值: 5)
            
        Returns:
            tuple: (status_code, tool_offset_data)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - tool_offset_data: IODBTO_1_3結構實例（成功時）或None（失敗時）
        """
        # 讀取偏移類型
        type = -3

        # 計算數據長度
        length = 8 + (e_number - s_number + 1) * 16
        
        # 創建 IODBTO_1_3 結構實例
        tool_offset_data = IODBTO_1_3()
        
        # 調用 cnc_rdtofsr 函數
        ret = self.focas.cnc_rdtofsr(
            handle, 
            s_number, 
            type, 
            e_number, 
            length, 
            byref(tool_offset_data)
        )
        
        if ret == Status.EW_OK:
            return ret, tool_offset_data
        else:
            return ret, None

    def CNC_ReadToolOffsetInfo(self, handle):
        """讀取工具偏移信息（對應C#的cnc_rdtofsinfo函數）
        
        Args:
            handle: CNC句柄
            
        Returns:
            tuple: (status_code, tool_offset_info)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - tool_offset_info: 包含工具偏移信息的字典（成功時）或None（失敗時）
                    - ofs_type: 工具偏移記憶體類型數值
                        - 0: memory type A
                        - 1: memory type B
                        - 2: memory type C
                    - ofs_type_desc: 工具偏移記憶體類型描述字串
                    - use_no: 可用的工具偏移數量
        """
        # 創建 ODBTLINF 結構實例
        tool_offset_info = ODBTLINF()
        
        # 調用 cnc_rdtofsinfo 函數
        ret = self.focas.cnc_rdtofsinfo(handle, byref(tool_offset_info))
        
        if ret == Status.EW_OK:
            # 解析偏移類型
            ofs_type = tool_offset_info.ofs_type
            ofs_type_desc = {
                0: "memory type A",
                1: "memory type B", 
                2: "memory type C"
            }.get(ofs_type, f"未知類型({ofs_type})")
            
            # 解析工具偏移信息
            offset_info = {
                'ofs_type': ofs_type_desc,
                'use_no': tool_offset_info.use_no
            }
            
            return ret, offset_info
        else:
            return ret, None

    def CNC_ReadCurrentToolData(self, handle):
        """讀取當前工具數據（對應C#的cnc_rd1tlifedata函數）
        
        Args:
            handle: CNC句柄
            
        Returns:
            tuple: (status_code, tool_data)
                - status_code: 狀態碼，0表示成功，其他值表示錯誤
                - tool_data: 包含當前工具數據的字典（成功時）或None（失敗時）
                    - groupno: 工具組號
                    - tool_num: 工具號
                    - h_code: H代碼
                    - d_code: D代碼
                    - tool_inf: 工具信息數值
                    - tool_inf_desc: 工具信息描述
                        - 0: (refer to the following)
                        - 1: This tool is registered(available).
                        - 2: This tool has expired.
                        - 3: This tool was skipped.
        """
        grp_no = 0
        tool_no = 0
        # 創建 IODBTD 結構實例
        tool_data = IODBTD()
        
        # 調用 cnc_rd1tlifedata 函數
        ret = self.focas.cnc_rd1tlifedata(handle, grp_no, tool_no, byref(tool_data))
        
        if ret == Status.EW_OK:
            # 解析工具信息
            tool_inf_value = tool_data.tool_inf
            tool_inf_descriptions = {
                0: "(refer to the following)",
                1: "This tool is registered(available).",
                2: "This tool has expired.",
                3: "This tool was skipped."
            }
            tool_inf_desc = tool_inf_descriptions.get(tool_inf_value, f"未知工具信息({tool_inf_value})")
            
            # 解析工具壽命數據
            tool_info = {
                'groupno': tool_data.datano,      # 工具組號
                'tool_num': tool_data.tool_num,  # 工具號
                'h_code': tool_data.h_code,      # H代碼
                'd_code': tool_data.d_code,      # D代碼
                'tool_inf': tool_inf_value,      # 工具信息數值
                'tool_inf_desc': tool_inf_desc   # 工具信息描述
            }
            
            return ret, tool_info
        else:
            return ret, None


# 使用範例
if __name__ == "__main__":
    try:
        # 創建 MachsyncFocas 實例
        cnc = MachsyncFocas("Fwlib64.dll")  # 請替換為實際的 DLL 路徑
        
        # 連接到 CNC（請替換為實際的 IP 地址）
        ret, handle = cnc.CNC_Connect("*************")
        if ret == Status.EW_OK:
            print(f"成功連接到 CNC，句柄：{handle}")
            
            # 讀取動態數據
            status, dynamic_data = cnc.CNC_ReadDynamicData(handle)
            if status == Status.EW_OK and dynamic_data is not None:
                print(f"軸數：{dynamic_data.axis}")
                print(f"程式號：{dynamic_data.prgnum}")
                print(f"絕對位置 X：{dynamic_data.absolutePos.x}, Y：{dynamic_data.absolutePos.y}, Z：{dynamic_data.absolutePos.z}")
                print(f"相對位置 X：{dynamic_data.relativePos.x}, Y：{dynamic_data.relativePos.y}, Z：{dynamic_data.relativePos.z}")
                print(f"機器位置 X：{dynamic_data.machinePos.x}, Y：{dynamic_data.machinePos.y}, Z：{dynamic_data.machinePos.z}")
            else:
                print(f"讀取動態數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
                if dynamic_data is not None:
                    print(f"部分數據：{dynamic_data}")
            
            # 讀取軸名稱
            status, axis_num, axis_names = cnc.CNC_ReadAxisNames(handle)
            if status == Status.EW_OK:
                print(f"軸數：{axis_num}")
                print(f"軸名稱：{axis_names}")
            else:
                print(f"讀取軸名稱失敗，錯誤碼：{status}")
            
            # 讀取G代碼數據
            status, num_gcd, gcode_list = cnc.CNC_ReadGCode(handle)
            if status == Status.EW_OK:
                print(f"G代碼數量：{num_gcd}")
                print(f"有效G代碼數量：{len(gcode_list)}")
                
                # 顯示前幾個G代碼
                for i, gcode_info in enumerate(gcode_list[:5]):  # 只顯示前5個
                    flag_info = gcode_info['flag_info']
                    print(f"G代碼 {gcode_info['index']}: 組別={gcode_info['group']}, "
                          f"標誌={gcode_info['flag']} (0x{gcode_info['flag']:02X}), "
                          f"代碼='{gcode_info['code']}'")
                    print(f"  Flag解析: 與前一塊不同={flag_info['bit3_command_diff']}, "
                          f"有命令={flag_info['bit7_has_command']}")
            else:
                print(f"讀取G代碼失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取速度數據
            status, speed_data = cnc.CNC_ReadSpeedData(handle)
            if status == Status.EW_OK:
                feedrate = speed_data['actual_feedrate']
                spindle = speed_data['actual_spindle_speed']
                
                print(f"實際進給速度：{feedrate['data']} {feedrate['unit']} "
                      f"(小數位：{feedrate['decimal_position']}, "
                      f"顯示標誌：{feedrate['display_flag']}, "
                      f"名稱：'{feedrate['name']}', "
                      f"後綴：'{feedrate['suffix']}')")
                print(f"實際主軸速度：{spindle['data']} {spindle['unit']} "
                      f"(小數位：{spindle['decimal_position']}, "
                      f"顯示標誌：{spindle['display_flag']}, "
                      f"名稱：'{spindle['name']}', "
                      f"後綴：'{spindle['suffix']}')")
            else:
                print(f"讀取速度數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取位置數據
            status, data_num, position_data = cnc.CNC_ReadPositionData(handle)
            if status == Status.EW_OK:
                print(f"位置數據讀取成功，實際讀取軸數：{data_num}")
                
                # 顯示前幾個軸的位置數據
                for axis_key in list(position_data.keys())[:3]:  # 只顯示前3個軸
                    axis_info = position_data[axis_key]
                    print(f"\n{axis_key}:")
                    
                    for pos_type, pos_data in axis_info.items():
                        print(f"  {pos_type}: {pos_data['data']} {pos_data['unit']} "
                              f"(小數位：{pos_data['decimal_position']}, "
                              f"軸名稱：'{pos_data['full_axis_name']}', "
                              f"顯示狀態：{pos_data['display_status']})")
            else:
                print(f"讀取位置數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取系統信息
            status, system_info = cnc.CNC_ReadSystemInfo(handle)
            if status == Status.EW_OK:
                print(f"\n系統信息讀取成功：")
                print(f"  附加信息原始值：{system_info['addinfo']}")
                
                # 顯示解析後的 addinfo 信息
                addinfo_parsed = system_info['addinfo_parsed']
                print(f"  附加信息：{addinfo_parsed}")
                
                print(f"  最大軸數：{system_info['max_axis']}")
                
                # 顯示解析後的 CNC 類型信息
                cnc_type_parsed = system_info['cnc_type_parsed']
                print(f"  CNC類型原始值：'{system_info['cnc_type']}'")
                print(f"  CNC類型：{cnc_type_parsed}")
                
                # 顯示解析後的機器類型信息
                mt_type_parsed = system_info['mt_type_parsed']
                print(f"  機器類型原始值：'{system_info['mt_type']}'")
                print(f"  機器類型：{mt_type_parsed}")
                print(f"  系列：{system_info['series']}")
                print(f"  版本：{system_info['version']}")
                print(f"  軸：{system_info['axes']}")
            else:
                print(f"讀取系統信息失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取除G代碼外的模態數據
            print(f"\n=== 讀取除G代碼外的模態數據 ===")
            status, num_commands, command_list = cnc.CNC_ReadModalDataExceptGCode(handle)
            if status == Status.EW_OK:
                print(f"模態數據讀取成功，實際讀取數量：{num_commands}")
                print(f"有效命令數量：{len(command_list)}")
                
                # 顯示前幾個命令
                for i, cmd_info in enumerate(command_list[:5]):  # 只顯示前5個
                    flag_info = cmd_info['flag_info']
                    print(f"命令 {cmd_info['index']}: 地址='{cmd_info['address']}', "
                          f"編號={cmd_info['number']}, 標誌=0x{cmd_info['flag']:04X}")
                    print(f"  Flag解析: 與前一塊不同={flag_info['bit3_command_diff']}, "
                          f"有小數點={flag_info['bit4_has_decimal']}, "
                          f"負數={flag_info['bit5_negative']}, "
                          f"一位數={flag_info['bit6_one_digit']}, "
                          f"RESET後命令={flag_info['bit11_after_reset']}, "
                          f"當前塊有命令={flag_info['bit15_in_present_block']}")
                    print(f"  命令值={cmd_info['command_value']}, 小數值={cmd_info['decimal_value']}")
            else:
                print(f"讀取模態數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取所有命令數據
            print(f"\n=== 讀取所有命令數據 ===")
            status, num_commands, command_list = cnc.CNC_ReadCommandedData(handle)
            if status == Status.EW_OK:
                print(f"所有命令數據讀取成功，實際讀取數量：{num_commands}")
                print(f"有效命令數量：{len(command_list)}")
                
                # 顯示前幾個命令
                for i, cmd_info in enumerate(command_list[:5]):  # 只顯示前5個
                    flag_info = cmd_info['flag_info']
                    print(f"命令 {cmd_info['index']}: 地址='{cmd_info['address']}', "
                          f"編號={cmd_info['number']}, 標誌=0x{cmd_info['flag']:04X}")
                    print(f"  Flag解析: 有小數點={flag_info['bit4_has_decimal']}, "
                          f"負數={flag_info['bit5_negative']}, "
                          f"一位數={flag_info['bit6_one_digit']}, "
                          f"RESET後命令={flag_info['bit11_after_reset']}, "
                          f"當前塊有命令={flag_info['bit15_in_present_block']}")
                    print(f"  命令值={cmd_info['command_value']}, 小數值={cmd_info['decimal_value']}")
            else:
                print(f"讀取所有命令數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取與軸相關的所有命令數據
            print(f"\n=== 讀取與軸相關的所有命令數據 ===")
            status, num_commands, command_list = cnc.CNC_ReadCommandedDataConcerningAxis(handle)
            if status == Status.EW_OK:
                print(f"軸相關命令數據讀取成功，實際讀取數量：{num_commands}")
                print(f"有效命令數量：{len(command_list)}")
                
                # 顯示前幾個命令
                for i, cmd_info in enumerate(command_list[:5]):  # 只顯示前5個
                    flag_info = cmd_info['flag_info']
                    print(f"命令 {cmd_info['index']}: 地址='{cmd_info['address']}', "
                          f"編號={cmd_info['number']}, 標誌=0x{cmd_info['flag']:04X}")
                    print(f"  Flag解析: 有小數點={flag_info['bit4_has_decimal']}, "
                          f"負數={flag_info['bit5_negative']}, "
                          f"一位數={flag_info['bit6_one_digit']}, "
                          f"RESET後命令={flag_info['bit11_after_reset']}, "
                          f"當前塊有命令={flag_info['bit15_in_present_block']}")
                    print(f"  命令值={cmd_info['command_value']}, 小數值={cmd_info['decimal_value']}")
            else:
                print(f"讀取軸相關命令數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取計時器數據（日期）
            print(f"\n=== 讀取計時器數據（日期）===")
            status, timer_data = cnc.CNC_ReadTimerData(handle, timer_type=0)
            if status == Status.EW_OK:
                print(f"計時器數據讀取成功（日期）：")
                print(f"  類型：{timer_data['type']}")
                print(f"  日期：{timer_data['date']['year']}-{timer_data['date']['month']:02d}-{timer_data['date']['date']:02d}")
            else:
                print(f"讀取計時器數據（日期）失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取計時器數據（時間）
            print(f"\n=== 讀取計時器數據（時間）===")
            status, timer_data = cnc.CNC_ReadTimerData(handle, timer_type=1)
            if status == Status.EW_OK:
                print(f"計時器數據讀取成功（時間）：")
                print(f"  類型：{timer_data['type']}")
                print(f"  時間：{timer_data['time']['hour']:02d}:{timer_data['time']['minute']:02d}:{timer_data['time']['second']:02d}")
            else:
                print(f"讀取計時器數據（時間）失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取工具偏移數據演示
            print(f"\n=== 讀取工具偏移數據演示 ===")
            status, tool_offset_data = cnc.CNC_ReadToolOffsetData(handle)
            if status == Status.EW_OK:
                print(f"工具偏移數據讀取成功：")
                print(f"  開始偏移編號：{tool_offset_data.datano_s}")
                print(f"  偏移類型：{tool_offset_data.type}")
                print(f"  結束偏移編號：{tool_offset_data.datano_e}")
                
                # 顯示前幾個工具的偏移數據
                print(f"\n工具偏移數據內容：")
                for i in range(min(3, 5)):  # 只顯示前3個工具
                    base_index = i * 4
                    print(f"  工具 {i}:")
                    print(f"    工具長度/幾何：{tool_offset_data.ofs.m_ofs_c[base_index + 0]}")
                    print(f"    工具長度/磨損：{tool_offset_data.ofs.m_ofs_c[base_index + 1]}")
                    print(f"    刀具半徑/幾何：{tool_offset_data.ofs.m_ofs_c[base_index + 2]}")
                    print(f"    刀具半徑/磨損：{tool_offset_data.ofs.m_ofs_c[base_index + 3]}")
            else:
                print(f"讀取工具偏移數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取工具偏移信息演示
            print(f"\n=== 讀取工具偏移信息演示 ===")
            status, tool_offset_info = cnc.CNC_ReadToolOffsetInfo(handle)
            if status == Status.EW_OK:
                print(f"工具偏移信息讀取成功：")
                print(f"  偏移類型：{tool_offset_info['ofs_type_desc']}")
                print(f"  可用工具偏移數量：{tool_offset_info['use_no']}")
            else:
                print(f"讀取工具偏移信息失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 讀取當前工具數據演示
            print(f"\n=== 讀取當前工具數據演示 ===")
            status, tool_life_data = cnc.CNC_ReadCurrentToolData(handle)
            if status == Status.EW_OK:
                print(f"當前工具數據讀取成功：")
                print(f"  工具組號：{tool_life_data['groupno']}")
                print(f"  工具號：{tool_life_data['tool_num']}")
                print(f"  H代碼：{tool_life_data['h_code']}")
                print(f"  D代碼：{tool_life_data['d_code']}")
                print(f"  工具信息數值：{tool_life_data['tool_inf']}")
                print(f"  工具信息描述：{tool_life_data['tool_inf_desc']}")
            else:
                print(f"讀取當前工具數據失敗，錯誤碼：{status}")
                print(f"錯誤描述：{cnc.GetStatusDescription(status)}")
            
            # 斷開連接
            cnc.CNC_Disconnect(handle)
        else:
            print(f"連接失敗，錯誤碼：{ret}")
    
    except Exception as e:
        print(f"錯誤：{e}")