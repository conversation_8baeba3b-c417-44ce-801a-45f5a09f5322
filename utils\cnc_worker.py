from PySide2.QtCore import QThread, Signal
import time
from . import logger  # 從同一個包導入 logger

from utils.MachsyncFOCAS import MachsyncFocas

class CNCWorker(QThread):

    sig_cnc_data_received = Signal(object)
    sig_cnc_connect_failed = Signal()
    sig_cnc_disconnected = Signal()

    def __init__(self, host='**************', port=8193, timeout=3):
        super().__init__()
        self.host = host
        self.port = port
        self.timeout = timeout
        self.cnc = None
        self.cnc_handle = None
        
        self.stopped = False

        self.simulation_mode = True
        
    def __del__(self):
        logger.error(f"CNCWorker {self.host} 已被銷毀")

    def run(self):
        try:
            if not self.simulation_mode:
                # 如果從專案根目錄執行
                self.cnc = MachsyncFocas("resources/fanuc-dll/Fwlib64.dll")
                ret, self.cnc_handle = self.cnc.CNC_Connect(self.host, self.port, self.timeout)
                if ret != 0:
                    logger.error(f"CNCWorker {self.host} 連接失敗: {ret}")
                    return

            logger.info(f"CNCWorker {self.host} 連接成功")

            speed = 100
            acc = 100

            while True:
                if self.stopped:
                    Stopped = False
                    if not self.simulation_mode:
                        self.cnc.CNC_Disconnect(self.cnc_handle)
                    logger.info(f"CNCWorker {self.host} 已斷開連接")
                    break

                if self.simulation_mode:
                    dynamic_data = {
                        "cnc_type": "Series 30i",
                        "acts": speed,
                        "tool_num": 6,
                    }
                    self.sig_cnc_data_received.emit(dynamic_data)

                    speed += acc
                    if speed > 2000:
                        acc = -100
                    elif speed < 100:
                        acc = 100

                    time.sleep(5)
                    continue

                ret, dynamic_data = self.cnc.CNC_ReadDynamicData(self.cnc_handle)
                if ret != 0:
                    logger.error(f"CNCWorker {self.host} 讀取動態資料失敗: {ret}")
                    self.sig_cnc_disconnected.emit()
                    continue

                logger.info(f"CNCWorker {self.host} 讀取動態資料成功: {dynamic_data}")

                ret, tool_info = self.cnc.CNC_ReadToolInfo(self.cnc_handle)
                if ret != 0:
                    logger.error(f"CNCWorker {self.host} 讀取工具資料失敗: {ret}")
                    continue

                ret, system_info = self.cnc.CNC_ReadSystemInfo(self.cnc_handle)
                if ret != 0:
                    logger.error(f"CNCWorker {self.host} 讀取系統資料失敗: {ret}")
                    continue
                
                data = {
                    "cnc_type": system_info["cnc_type_parsed"],
                    "acts": dynamic_data.acts,
                    "tool_info": tool_info["tool_num"],
                }

                self.sig_cnc_data_received.emit(data)


                time.sleep(5)

        except Exception as e:
            logger.error(f"CNCWorker {self.host} 初始化失敗: {e}")
            return

    def stop(self):
        self.stopped = True

