import os
from . import logger

# TODO: 不要分那麼多模組，可能試著跟TextFileManager合併
class RecordFileManager:
    """在指定的目錄中創建文字檔案(唯一)，並提供檔案操作功能"""

    def __init__(self, directory, filename):
        """initialize file manager"""
        # directory
        self.directory = directory
        if not os.path.exists(self.directory):
            try:
                os.makedirs(self.directory)
                logger.debug(f"目錄 '{self.directory}' 創建成功。")
            except Exception as e:
                logger.error(f"目錄 '{self.directory}' 創建失敗：{e}")
                # raise Exception(f"Failed to create directory: {self.directory}")
        
        # file name and handle
        self.filename = os.path.join(directory, filename)
        # create file
        try:
            # check if file exists (policy: not to overwrite existing file)
            if os.path.exists(self.filename):
                logger.error(f"檔案 '{self.filename}' 已存在")
                raise Exception(f"File already exists: {self.filename}")
            self.file_handle = open(self.filename, "w+", encoding="utf-8")
            logger.debug(f"檔案 '{filename}' 建立成功")
        except Exception as e:
            logger.error(f"檔案 '{filename}' 建立失敗")
            # raise Exception(f"Failed to create file: {filename} in directory: {directory}")

    def add_text(self, text):
        """在文字檔中新增文字。"""
        try:
            self.file_handle.write(text)
            self.file_handle.flush()
            os.fsync(self.file_handle.fileno())
            # logger.debug(f"文字已新增到檔案 '{self.filename}'。")
        except Exception as e:
            logger.error(f"新增文字到檔案 '{self.filename}' 失敗：{e}")

    def delete_text(self, text_to_delete):
        """從文字檔中刪除指定文字。"""
        try:
            self.file_handle.seek(0)  # Rewind to the beginning before reading
            lines = self.file_handle.readlines()
            
            # Filter out the lines containing the text to delete
            lines_to_keep = [line for line in lines if text_to_delete not in line]
            
            self.file_handle.seek(0)      # Rewind to the beginning before writing
            self.file_handle.truncate()   # Clear the file content
            self.file_handle.writelines(lines_to_keep)
            self.file_handle.flush()      # Flush the buffer to disk
            
            logger.debug(f"已從檔案 '{self.filename}' 中刪除文字 '{text_to_delete}'。")
        except Exception as e:
            logger.error(f"從檔案 '{self.filename}' 中刪除文字失敗：{e}")

    def close_file(self):
        """關閉文字檔 (記得要呼叫)"""
        try:
            self.file_handle.close()
            logger.debug(f"檔案 '{self.filename}' 已關閉。")
        except Exception as e:
            logger.error(f"關閉檔案 '{self.filename}' 失敗：{e}") 