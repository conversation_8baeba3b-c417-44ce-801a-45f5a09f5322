from PySide2.QtCore import QThread, Signal
import socket
import time
import math
from utils.cirular_queue import CircularQueue
from . import logger  # 從同一個包導入 logger

class SocketWorker(QThread):
    # raw_data_received = Signal(str)  # 當收到數據時發出 Signal
    raw_data_received = Signal(str)  # 發送原始數據信號
    sig_socket_connect = Signal(object)  # 發送 Socket 信號
    sig_connection_success = Signal()
     
    def __init__(self, host='*************', port=1333,sampleRate=10000):
        super().__init__()
        self.host = host
        self.port = port
        self.SampleRate = sampleRate # 10000  # 取資料
        self.SamplePoint1 = math.ceil(self.SampleRate / 12.5)  
        self.sample_N = math.ceil(self.SamplePoint1 * 1.005)
        self.sample_byte = int(self.sample_N * 16)

        self.running = False
        self.paused = False  # **新增：控制是否暫停數據接收**
        self.sock = None
        logger.info("SocketWorker Init")

        # self.data_queue = CircularQueue(maxsize=10)
        self.collect_data = ""  # 存放累積的數據
        self.temp_data = ""  # 存放不滿 32-byte 的數據

    def __del__(self):
        logger.error(f"SocketWorker {self.host} 已被銷毀")

    def run(self):
        """ 執行緒執行的 socket 讀取邏輯 """
        logger.info("SocketWorker RUN")
        self.running = True

        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
            self.sock.connect((self.host, self.port))
            self.sock.settimeout(3)
            logger.info(f"已連接到 {self.host}:{self.port}")
            self.sig_connection_success.emit()

            while self.running:
                if self.paused:  
                    logger.debug("DecoderWorker 暫停接收數據...")
                    time.sleep(0.5)  # **當暫停時，避免 CPU 高負載**
                    continue  

                try:
                    if self.sock is None:
                        logger.error("Socket 已關閉，停止讀取")
                        break
                    # logger.debug("DecoderWorker 正在接收數據...")

                    hex_data = self.receive_chunk(self.sample_N, self.sample_byte)  
                    if hex_data:
                        self.emit_data(hex_data)
                        # time.sleep(0.01)
                except socket.timeout:
                    logger.warning("忽略超時錯誤，繼續監聽")
                    continue  
                except socket.error as e:
                    logger.error(f"Socket Error: {e}")
                    break  
        # TODO: Check 如果沒有盡到 finally 會發生其他問題嗎?    
        except Exception as e:
            logger.error(f"SocketWorker 運行時發生異常: {e}")
            # self.sig_socket_connect.emit(False)
        finally:
            self.close_socket()  
            logger.info("SocketWorker 已停止")
            data ={"status": False, "host": self.host}
            self.sig_socket_connect.emit(data)

        self.exec_()

    def pause(self):
        """暫停數據接收，但不關閉 Socket"""
        logger.info("暫停數據接收...")
        self.paused = True

    def resume(self):
        """恢復數據接收"""
        logger.info("恢復數據接收...")
        self.paused = False

    def stop(self):
        """ 安全停止執行緒 """
        logger.info("SocketWorker 正在停止...")
        self.running = False
        self.close_socket()

        self.quit()  # ✅ 正確終止事件迴圈
        if not self.wait(1000):  # ✅ 最多等待 1 秒，避免 UI 當機
            logger.warning("SocketWorker 未能正常結束，強制終止")
            self.terminate()  # 🚨 最後手段，強制終止
            self.wait()  # 確保完全停止

    def start_client(self):
        try:
            logger.info(f"start_client {self.host} 連線中")
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            self.running = True
        except socket.error as e:
            # print(f"Error starting client: {e}")
            logger.error(f"Error starting client: {e}")
            self.stop()

    def close_socket(self):
        """ 安全關閉 socket，避免錯誤 """
        if self.sock:
            try:
                logger.info("關閉 socket 連線...")
                self.sock.shutdown(socket.SHUT_RDWR)  # ✅ 避免 `WinError 10038`
            except Exception as e:
                logger.warning(f"Socket 關閉異常: {e}")
            finally:
                self.sock.close()
                self.sock = None  # ✅ 確保 socket 變數不再使用
                logger.info("Socket 已關閉")

    def send_sleepmode(self,is_sleep_on):
        if not is_sleep_on: #睡眠未開啟
            self.sock.send("sleep_on\n".encode())
            logger.error("sleep_on")
        else: #睡眠開啟
            self.sock.send("sleep_off\n".encode())
            logger.error("sleep_off")

    def process_data(self, hex_data):
        """負責整理數據，每 32-byte 為一組"""
    
        # 累積數據
        if self.temp_data:
            hex_data = self.temp_data + hex_data  # 加上上次未處理的數  據
    
        N = len(hex_data) // 64  # 計算完整的 32-byte 區塊數量
        complete_data = hex_data[:N*64]  # 取出完整的 32-byte 數據
        self.temp_data = hex_data[N*64:]  # 更新未處理的部分
    
        return complete_data  # 回傳完整數據

    def emit_data(self, hex_data):
        """負責發送數據到主執行緒"""
        if hex_data:
            # logger.debug(f"發送數據: {hex_data[:50]}... (共 {len(hex_data)} HEX 字符)")
            self.raw_data_received.emit(hex_data)  # 發送完整的 32-byte 數據

    # 刀把資料收集-持續接收資料，直到接收完整的一段資料後回傳
    def receive_chunk(self, sample_N , sample_byte):
        # print("client 取資料中...")
        # logger.debug("client 取資料中...")
        count_get = 0
        # sample_byte = int(sample_N * 16)
        collect_data = ""
        self.temp_data = ""

        try:
            if not self.running or not self.sock:
                raise Exception("Client is not running or socket is invalid.")
            
            while count_get < sample_N:
                data_recv = self.sock.recv(sample_byte).hex()
                if not data_recv:
                    raise Exception("Connection closed by the server.")
                collect_data += data_recv

                if self.temp_data:
                    data_recv = self.temp_data + data_recv

                N = len(data_recv) // 32
                count_get += N

                if len(data_recv) % 32 > 0:
                    self.temp_data = data_recv[N*32:]
                else:
                    self.temp_data = []

                # ✅ 避免計算錯誤
                remaining_samples = sample_N - count_get
                sample_byte = max((remaining_samples * 32 - len(self.temp_data)) // 2, 1)

            # print("client 取資料完...")
            # logger.debug("client 取資料完...")
            return collect_data

        except socket.error as e:
            # print(f"Socket error: {e}")
            logger.error(f"Socket error: {e}")
            self.stop()
            raise


    

    # def run(self):
    #     """ 執行緒執行的 socket 讀取邏輯 """
    #     self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    #     self.sock.connect((self.host, self.port))
    #     self.sock.settimeout(1)  # 設定超時，避免阻塞

    #     while self.running:
    #         try:
    #             data = self.sock.recv(1024)
    #             if data:
    #                 self.data_received.emit(data.decode())  # 發送數據到主執行緒
    #         except socket.timeout:
    #             continue  # 忽略超時錯誤，繼續監聽
    #         except Exception as e:
    #             print(f"Socket Error: {e}")
    #             break
        
    #     self.sock.close()
